<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>8.0</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{569a85a2-4aab-f951-60bb-1b6fc96e8c70}</ProjectGuid>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\Assembly-CSharp\</OutputPath>
    <DefineConstants>UNITY_2020_3_47;UNITY_2020_3;UNITY_2020;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_MONO_BDWGC;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;PLATFORM_STANDALONE;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;ENABLE_WEBSOCKET_HOST;ENABLE_MONO;NET_STANDARD_2_0;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;AMPLIFY_SHADER_EDITOR;DISABLE_AIHELP;UNITY_POST_PROCESSING_STACK_V2;ODIN_INSPECTOR;ODIN_INSPECTOR_3;FISH_PORTRAIT;FISH_DEBUG;FISH_PAY;USE_HUATUO;PLAYMAKER;PLAYMAKER_1_9;PLAYMAKER_1_9_1;PLAYMAKER_1_8_OR_NEWER;PLAYMAKER_1_8_5_OR_NEWER;PLAYMAKER_1_9_OR_NEWER;PLAYMAKER_TMPRO;PLAYMAKER_UTILS;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169,0649</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorIKGoal.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\LitJson\JsonMockWrapper.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\SetMass.cs" />
    <Compile Include="Assets\PlayMaker\Actions\EaseEditor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\TestAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigItem.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\SetIntFromFloat.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Gamepad\GamepadActionBase.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Color\ColorRamp.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetTagCount.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Material\SetTextureOffset.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorPlayBackSpeed.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\GetParticleCollisionInfo.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Selectable\UiSetAnimationTriggers.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\TranslatePosition2d.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\audio\FMODWrapper.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\GUITooltip.cs" />
    <Compile Include="Assets\ThirdPart\Unity-Logs-Viewer\Reporter\Test\Rotate.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerInput\PlayerInputGetBool.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRAdapter\IComparableAdapter.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UIGrid.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RenderSettings\SetHaloStrength.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Convert\ConvertIntToString.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Trigonometry\GetASine.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\SetWheelJoint2dProperties.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorLayersAffectMassCenter.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\UGuiGridLayoutSetCellSize.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldMoveCaretToTextEnd.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\ScreenPick2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Convert\ConvertMaterialToObject.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiToggleGetIsOn.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Layout\uGuiLayoutElementGetValues.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\Config.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutLabel.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiSliderSetMinMax.cs" />
    <Compile Include="Assets\GameScript\Runtime\sdk\SDKInterface.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldSetCharacterLimit.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\network\NetCenter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnPointerDownEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\EasingFunction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenUiSize.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\SetSprite.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\MainCameraTarget\MainCameraTarget.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\ResourceWindowHelper.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\network\WebFileRequest.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiSliderGetNormalizedValue.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiSliderGetWholeNumbers.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker uGui\Proxies\PlayMakerUGuiCanvasRaycastFilterEventsProxy.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetPlayOnAwake.cs" />
    <Compile Include="Assets\GameScript\Runtime\base\HDAdapter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\SetSpriteMaskInteraction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\AnimatorMatchTarget.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnMoveEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigEmoji.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\GetCLRPropertyAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\bi\BI.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldGetCaretBlinkRate.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\FsmTrialGame.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigMerryChristmasTips.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\LitJson\ParserToken.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnDropEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\UseGUILayout.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigUserName.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Gamepad\GamepadButtonEvents.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\Trails\Trail.cs" />
    <Compile Include="Assets\GameScript\Runtime\AssetBundle\GameLauncher.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\MergeNode\FsmExtractZip.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\AnimationEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\SetFsmArrayItem.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerInput\PlayerInputPerformedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\WaitAnyKey.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Convert\ConvertIntToFloat.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\IDAllocator.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\GetIPhoneSettings.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\ClampRotation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformPixelAdjustPoint.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PlayMakerUtils_Events.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerFsmTarget\PlayMakerFsmTarget.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\FloatChanged.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\SetIsKinematic2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetTargetTexture.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRCore\ILRRegister.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RenderSettings\SetSkybox.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerFsmVariable\PlayMakerFsmVariable.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Selectable\UiSetBlockColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Camera\WorldToScreenPoint.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Camera\SetBackgroundColor.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\BatteryDisplay.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\NetGmUI.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Canvas\UiCanvasScalerGetScaleFactor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\GetRaycastAllInfo.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\IntSwitch.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmTexture.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Gamepad\GamepadButtonComboEvents.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\SetSpriteFlip.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigScene.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\GetRayCastHit2dInfo.cs" />
    <Compile Include="Assets\GameScript\Runtime\AssetBundle\GameSupport.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\CurveVector3.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigBossEffect.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\LoopState.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\AddExplosionForce.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformGetAnchorMax.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutEndVertical.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\DynamicBone\DynamicBonePlaneCollider.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigWeapon.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetTargetCameraAlpha.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetTargetCameraAlpha.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Canvas\uGuiCanvasScalerGetScaleFactor.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PlayMakerUtils_Serialization.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\WindowBase.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\StringContains.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldGetTextAsFloat.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutEndHorizontal.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\LookAt.cs" />
    <Compile Include="Assets\Temporary\BossTest\BloodAngel\BloodAngelTest3.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\Vector3Compare.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\FsmHasVariable.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\GetStoreDataAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigAchievementFish.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetAudioOutputMode.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\Internal\GetSceneActionBase.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ProceduralMaterial\SetProceduralColor.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\audio\AudioSample.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\DrawStateLabel.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnSelectEvent.cs" />
    <Compile Include="Assets\GameRes\Shaders\TechY\ShadowPassOnOff.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\FloatSignTest.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\AddTorque.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Selectable\UiNavigationGetMode.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\StopLocationServiceUpdates.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetAspectRatio.cs" />
    <Compile Include="Assets\PlayMaker\UpdateHelper.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetEventInfo.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3Interpolate.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\ByteBuffer.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorFloat.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\GetSpriteMaskInteraction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayReverse.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorRightFootBottomHeight.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\Adapter.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiDropDownClearOptions.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorTarget.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnSelectEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\GetSpeed.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\LineManager.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorLayersAffectMassCenter.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiSliderSetDirection.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\FsmClearUnusedFiles.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\GetFsmArrayItem.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PlayMakerUtils.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetEventData.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\FinishFSM.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Selectable\UiSetIsInteractable.cs" />
    <Compile Include="Assets\GameScript\Runtime\sdk\MacInterface.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\BoolNoneTrue.cs" />
    <Compile Include="Assets\Temporary\BossTest\CaveExplorationTest\PlayerController.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\Effect\EffectDelay.cs" />
    <Compile Include="Assets\ThirdPart\Unity-Logs-Viewer\Reporter\Reporter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\RotateGUI.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\Trigger2dEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformGetPivot.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiGraphicCrossFadeColor.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UIPopupProperty.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\ReddotChild.cs" />
    <Compile Include="Assets\GameScript\Runtime\Component\UICloseController.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\UguiUiToWorldPoint.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiToggleGetIsOn.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRCore\ValueTypeBinder\QuaternionBinder.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorFloat.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\UGuiGridLayoutSetSpacing.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\DebugVector3.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker uGui\Proxies\PlayMakerUGuiDragEventsProxy.cs" />
    <Compile Include="Assets\GameRes\Shaders\Post Processing\Common\Settings\PostProcessorSettings.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\FloatAbs.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorFeetPivotActive.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\GetVector3XYZ.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\GameObjectTagSwitch.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\EResourceStates.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\PlayMakerUtility.cs" />
    <Compile Include="Assets\PlayMaker\Actions\MathExpression\Mathos\MathExpression.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\GameObjectNode.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetControlledAudioTrackMaxCount.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Gamepad\GamepadStickEvents.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\serialize\Ints.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\ResetInputAxes.cs" />
    <Compile Include="Assets\GameScript\Runtime\base\View.cs" />
    <Compile Include="Assets\GameScript\Runtime\base\Serialize\DockManager.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\FishManager.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiDropDownAddOptions.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmVector2.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\AnimateVector3.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerErrorEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\GetMouseX.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\IsFixedAngle2d.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiScrollRectSetVertical.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\Effect\ParticleScreenAdapter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerPrefs\PlayerPrefsGetInt.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetFrameCount.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigPublic.cs" />
    <Compile Include="Assets\Temporary\BossTest\FractalAlgorithm\FractalGenerator.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Components\Comment.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformScreenPointToLocalPointInRectangle.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\PlayMakerEventProxy\PlayMakerEventProxy.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ScriptControl\EnableBehaviour.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Mesh\GetVertexCount.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnPointerExitEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Canvas\UiCanvasForceUpdateCanvases.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiDropDownSetValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\IsKinematic.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\InterpolateTransform.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\NetUIDrag.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiScrollbarGetDirection.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\ActivityTips.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayForEach.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\SetSpriteSortPoint.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Selectable\UiExplicitNavigationGetProperties.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\CharacterShaderRender\ManualUpdateManager.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldOnEndEditEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Trigonometry\GetSine.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldSetSelectionColor.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldGetWasCanceled.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorCurrentTransitionInfo.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Time\RandomWait.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Quaternion\QuaternionBaseAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigUserLevel.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiSliderOnValueChangedEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\AssetBundle\BundleDecrypter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\EnableGUI.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetCanSetPlaybackSpeed.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\HasComponent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayGetNext.cs" />
    <Compile Include="Assets\GameScript\Runtime\AssetBundle\Res.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UIBg.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Rect\SetRectFields.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\boss\BossMultiplierGateCollider.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\OutLight\OutLightRTController.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldGetTextAsFloat.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Movie\StopMovieTexture.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\LinkerWizard\LinkerData.cs" />
    <Compile Include="Assets\GameScript\Runtime\Game.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmVector2.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\GenericAttributes\ExpectComponentAttribute.cs" />
    <Compile Include="Assets\GameScript\Runtime\Me.cs" />
    <Compile Include="Assets\PlayMaker\Actions\String\GetStringLeft.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\Effect\CameraMotionBlur.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\Touch Object 2d Event.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Time\PerSecond.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UILanguageLocalizeText.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\GalleryScroll.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\AnimatorPlay.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\FloatDivide.cs" />
    <Compile Include="Assets\PlayMaker\Actions\MathExpression\Mathos\MathParser.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetEventIntData.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\Vector2Compare.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\RemoveMixingTransform.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Gamepad\GamepadHelpers.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\CreateEmptyObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Components\PlayMakerUiFloatValueChangedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\SetAnimationWeight.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2Normalize.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmString.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorCurrentStateInfoIsTag.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Audio\SetAudioLoop.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\GUIContentAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\FsmStateSwitch.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Quaternion\QuaternionEuler.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Material\SetMaterialMovieTexture.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\Track.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\GameObjectChanged.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Selectable\uGuiSetBlockColor.cs" />
    <Compile Include="Assets\Temporary\BossTest\BloodAngel\BloodAngelManager.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\BackKey.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Color\SetColorValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldGetTextAsInt.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\AddMixingTransform.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Lights\SetLightIntensity.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Color\SetColorRGBA.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Camera\ScreenToWorldPoint.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SendEventByName.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\ParseJsonAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\BaseUpdateAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorIsHuman.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Quaternion\GetQuaternionMultipliedByQuaternion.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\SetBoolValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiGetLastPointerDataInfo.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiToggleOnValueChangedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\GUIVerticalSlider.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\EnableAnimation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiScrollbarGetValue.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigCannon.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigEggTips.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UIDebugRaycastOutline.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigForge.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\DetachChildren.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\GenericAttributes\FsmVariableTargetVariableAttribute.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\EaseVector3.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Convert\ConvertFloatToString.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmBool.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\GameViewInternal.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiSliderGetDirection.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\action\GActionManager.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\AddForce.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\action\EaseManager.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\ActivateGameObject.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\bullet\BulletNode.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\GetDeviceAcceleration.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Convert\ConvertBoolToInt.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\GetRotation.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiSliderGetValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\CreateObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\AnimationSettings.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiTextSetText.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetWaitForFirstFrame.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UIPopup.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformSetPivot.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\BlockEvents.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiImageSetFillAmount.cs" />
    <Compile Include="Assets\GameScript\Runtime\sdk\SDKEvents.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigAchievement.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorCurrentStateInfo.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\WarpTextExample.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UITagTriggerSprite.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiImageSetSprite.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\GetLocationInfo.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetEventVector3Data.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\Explosion.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmVariables.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\event\Events.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiScrollbarSetSize.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Canvas\uGuiCanvasEnableRaycastFilter.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\Effect\PlaneScreenAdapter.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UISimpleGuideMask.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UnityObject\SetProperty.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Trigonometry\GetACosine.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnInitializePotentialDragEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayAddRange.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnDeselectEvent.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiImageGetSprite.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetSkipOnDrop.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\DeviceVibrate.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishView1542.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Application\GetScreenHeight.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\GetAxis.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\CreateScene.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\UGuiGridLayoutStartCorner.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SequenceEvent.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Internal\PlayMakerUtilsDotNetExtensions.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\CurveFloat.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\launcher\SceneLauncher.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerFsmVariableTarget\PlayMakerFsmVariableTarget.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiScrollbarOnValueChangedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\GetSceneCount.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\serialize\Strings.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigMerryChristmas.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\SetGravity2dScale.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetSource.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldSetHideMobileInput.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldGetSelectionColor.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishViewGoldGod.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenRect.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\AutoDestroy.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2PerSecond.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetEventBoolData.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayContains.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetSource.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorBool.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\StringChanged.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\SetGravity2d.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\ParticleMask.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiGraphicCrossFadeColor.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UIEmptyImage.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\DebugFsmVariable.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\audio\FmodSoundStudioEmitter.cs" />
    <Compile Include="Assets\GameScript\Runtime\Component\UIBubbleController.cs" />
    <Compile Include="Assets\PlayMaker\Actions\String\StringAppend.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayLength.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\Path.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\GetAxisVector.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\GameObjectIsVisibleToCamera.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\GUIAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Trigonometry\GetCosine.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\SetVelocity2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerInput\PlayerInputTriggeredEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\PlayRandomAnimation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformPixelAdjustRect.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Screen\ScreenWrap.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutSpace.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenRotation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Camera\CutToCamera.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoClip\VideoClipGetFrameCount.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\RectCompare.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\GetNextLineCast2d.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiGetSelectedGameObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2RotateTowards.cs" />
    <Compile Include="Assets\GameScript\Runtime\AssetBundle\AssetNew.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Canvas\uGuiCanvasGroupSetAlpha.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Rect\RectContains.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\ResetGUIMatrix.cs" />
    <Compile Include="Assets\GameScript\Runtime\AssetBundle\GameDestroy.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\EnumSwitch.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\SetAnimationTime.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnUpdateSelectedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutButton.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ProceduralMaterial\SetProceduralBoolean.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldGetWasCanceled.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Canvas\UiCanvasEnableRaycast.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnEndDragEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\GetSprite.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\SetStoreDataAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\GameObjectIsVisible.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayDeleteAt.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\AnimatorInterruptMatchTarget.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformSetLocalPosition.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiScrollbarSetDirection.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\WakeUp.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\CryptoUtility.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\CurveRect.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\PlayAnimation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnDragEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\audio\FmodEventMapScriptable.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\GetMass2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\GetCollision2dInfo.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutEndCentered.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\EResourceOperation.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiScrollbarOnValueChangedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiImageGetFillAmount.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Material\SetMaterialTexture.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\TransformDirection.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Audio\SetAudioVolume.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Quaternion\QuaternionInverse.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\SendSceneLoadedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorInt.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerInput\PlayerInputActionBase.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\SetSpriteSortingLayerByName.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ScriptControl\AxisEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutSelectionGrid.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\Sleep2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3Subtract.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenPunch.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ScriptControl\AddScript.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerFrameDroppedEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRCore\ValueTypeBinder\Vector2Binder.cs" />
    <Compile Include="Assets\Temporary\BossTest\CaveExplorationTest\CaveExplorationTest.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Web\WWWObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetCanSetSkipOnDrop.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\SelectRandomVector2.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformGetAnchoredPosition.cs" />
    <Compile Include="Assets\ThirdPart\Unity-Logs-Viewer\Reporter\Test\TestReporter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\GetSpriteOrderInLayer.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Camera\CameraFadeIn.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\GetVelocity2d.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\OutLight\OutlinePostEffectCmdBuffer.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\KillDelayedEvents.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmMaterial.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiScrollbarSetDirection.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\ArtChangeFog.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\SetCollider2dIsTrigger.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Lights\SetLightColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetPlaybackSpeed.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ScriptControl\SendMessage.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformSetSizeDelta.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\Downloader\FileVerify.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\FloatInterpolate.cs" />
    <Compile Include="Assets\Temporary\BossTest\InfiniteGoldTest\InfiniteGoldTest.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\GameQuality.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetPlaybackSpeed.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\IntOperator.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\MoveTowards.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\FsmCheckAppVersion.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\DestroyComponent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\SendSceneUnLoadedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\AddComponent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\RandomEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\SetGUIBackgroundColor.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigMatchTask.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\AnimatorCrossFade.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\MouseLook2.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\GetSpriteFlip.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldSetAsterixChar.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\GetMass.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Components\PlayMakerUiPointerEvents.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Audio\AudioMute.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Trigonometry\GetAtan2.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\Rotate.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerPrefs\PlayerPrefsSetString.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmInt.cs" />
    <Compile Include="Assets\PlayMaker\Actions\String\GetStringRight.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\GUIHorizontalSlider.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Selectable\uGuiSetIsInteractable.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\SetGUIDepth.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutFlexibleSpace.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\MovePosition.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetWaitForFirstFrame.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmVariable.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\GetCollisionInfo.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmState.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Convert\ConvertEnumToString.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldGetIsFocused.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\GetHotBehaviourAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigCompetition.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Selectable\uGuiSetAnimationTriggers.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\SetSpriteOrderInLayer.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldGetTextAsInt.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\DebugBool.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\Trails\TrailRenderer_Base.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorPlaybackTime.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiRebuild.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerPrefs\PlayerPrefsGetFloat.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2LowPassFilter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Selectable\UiGetIsInteractable.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiSliderSetWholeNumbers.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Components\PlayMakerCanvasRaycastFilterProxy.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\GameView.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\utils\UtilsString.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\AnimationEvents.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\DebugVector2.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigNewShare.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiScrollRectSetNormalizedPosition.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiRawImageSetRaycastTarget.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmEnum.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2ClampMagnitude.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\SetJointConnectedBody.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmColor.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\utils\UtilsMath.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Selectable\uGuiNavigationGetMode.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnUpdateSelectedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorPlayBackSpeed.cs" />
    <Compile Include="Assets\GameScript\Runtime\sdk\Certificate.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformSetOffsetMax.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Audio\AudioPause.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Character\ControllerMoveInAir.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\DestroySelf.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\LookAt2dGameObject.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\MessageTip.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\SetGUISkin.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\LateUpdateEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRCore\ValueTypeBinder\Vector3Binder.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigLottery.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Convert\ConvertStringToInt.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\LineCast2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Quaternion\QuaternionLerp.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\MousePick2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetPlayOnAwake.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldGetPlaceHolder.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\MousePickEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\Downloader\FileResumeDownloader.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Selectable\UiTransitionGetType.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenScale.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2Interpolate.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\AnimatorStartPlayback.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3ClampMagnitudeXZ.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\network\NetBuffer.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiSliderSetDirection.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker uGui\Proxies\PlayMakerUGuiPointerEventsProxy.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UISpineAdapt.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\serialize\Fonts.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\UGuiGridLayoutChildAlignment.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\serialize\Bools.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\GetSceneRootCount.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformScreenPointToWorldPointInRectangle.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2SnapToAngle.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetAudioTrackCount.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SendEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishGoldExplodeNode.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigPaotai.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\ShowSpeedControl.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorLayerWeight.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldGetHideMobileInput.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiDropDownClearOptions.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\GetDeviceRoll.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\SwimAnimation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformSetAnchorMinAndMax.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Components\PlayMakerUiDropEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\GetTrigger2dInfo.cs" />
    <Compile Include="Assets\HybridCLRGenerate\AOTGenericReferences.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmGameObject.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigLanguage.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\MoveObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmVector3.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerInput\PlayerInputCanceledEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRScprits\GameObjectList.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\GameObjectIsChildOf.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3Add.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\ResourceEventMessageDefine.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Camera\CameraFadeOut.cs" />
    <Compile Include="Assets\Temporary\BossTest\BloodAngel\BloodAngelTest1.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldOnSubmitEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\CharacterShaderRender\SetCameraShaderVariants.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\ButtonAttribute\ButtonAttribute.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Internal\EventDataSenderProxy.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\Owner\OwnerClass.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\SetHingeJoint2dProperties.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3SnapToGrid.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnDropEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerPause.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigFish.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\SetTagsOnChildren.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Quaternion\QuaternionRotateTowards.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Components\PlayMakerUiEventBase.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Rect\SetRectFromPoints.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\EnumCompare.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Trigonometry\GetAtan2FromVector3.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayShuffle.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\SelectRandomFloat.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiImageSetFillAmount.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiScrollRectSetHorizontal.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiRawImageSetTexture.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\SetMass2d.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\CharacterShaderRender\CharacterRenderManager.cs" />
    <Compile Include="Assets\Temporary\BossTest\BloodAngel\BloodAngelTest2.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Character\GetControllerCollisionFlags.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\utils\UtilsCommon.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiSliderSetNormalizedValue.cs" />
    <Compile Include="Assets\PlayMaker\Extension\Scripts\uGui\InputNavigator.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishView1512.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutHorizontalSlider.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UISkewImage.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\AddTorque2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RenderSettings\SetFlareStrength.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2AddXY.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigLotteryUnion.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\MajiaImageAdapter.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\UGuiGridLayoutStartAxis.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\FloatMultiply.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutBox.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorBody.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetRandomObject.cs" />
    <Compile Include="Assets\GameScript\Runtime\AssetBundle\AutoReleaseHandle.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\CurveFsmAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\audio\ObjectPool.cs" />
    <Compile Include="Assets\GameScript\Runtime\base\GuidePostEvent.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker uGui\Proxies\PlayMakerUGuiSceneProxy.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Movie\PauseMovieTexture.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutTextField.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Application\ApplicationRunInBackground.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnPointerEnterEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\action\GActionInstant.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\Downloader\FileGeneralDownloader.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RenderSettings\SetFogColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\GetspriteSortPoint.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\BulletManager.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorBody.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\DestroyObjects.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\GameEffectPositionNode.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformSetScreenPosition.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldMoveCaretToTextStart.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerPrefs\PlayerPrefsSetFloat.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnPointerExitEvent.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Extensions\PlayMakerUtils_extensions.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\SetGravity.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetName.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnPointerEnterEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiImageGetSprite.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\bi\BIEvents.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutPasswordField.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRCore\StaticMethod\IStaticMethod.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetEventStringData.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\BaseActions\TweenActionBase.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenAudio.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RenderSettings\EnableFog.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldActivate.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorLayerCount.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Effects\ParticleSystemPlay.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorRoot.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenUiPosition.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\SortingNodeManager.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorCurrentStateInfoIsName.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\RunFSMAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\GetPosition.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Components\PlayMakerUiVector2ValueChangedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorCullingMode.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Selectable\uGuiGetIsInteractable.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigCannonEffect.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\GameObjectIsNull.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformWorldToScreenPoint.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\event\EventCenter.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\FsmApplicationInit.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiDropDownGetSelectedData.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetChildCount.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\LoadSceneAsynch.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRCore\ILRDefine.cs" />
    <Compile Include="Assets\GameScript\Runtime\sdk\HWIOSInterface.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigBigMonsterReward.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Components\PlayMakerUiDragEvents.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\LookAt2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\String\GetStringLength.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\IntAdd.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\GetKey.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiSliderGetWholeNumbers.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerPrefs\PlayerPrefsDeleteKey.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\WakeAllRigidBodies2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\ProjectLocationToMap.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigLanguageLocalize.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\FsmStateTest.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\boot\SceneBoot.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\GetJointBreak2dInfo.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UIRaycaster.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Character\ControllerIsGrounded.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\GetSceneIsDirty.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldActivate.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\BroadcastEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\BoolFlip.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Convert\ConvertBoolToString.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\serialize\Materials.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\SampleCurve.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigWing.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\Delay.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\SetIsFixedAngle2d.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishColliderCircle.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\GetKeyUp.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Event Properties\Actions\SetEventProperties.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\ScreenPick.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnCancelEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\Component\UITextFormatter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetTimeSource.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\UGuiGridLayoutSetConstraint.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\ResourceEventDispatcher.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetEventFloatData.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldSetText.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutEmailField.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmObject.cs" />
    <Compile Include="Assets\GameScript\Runtime\AssetBundle\GameReload.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\SetGUIAlpha.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldGetPlaceHolder.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\SetCLRPropertyAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\network\WebHeaderRequest.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\SetActiveScene.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmRect.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Quaternion\GetQuaternionFromRotation.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnDeselectEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigFishFrame.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Enum\SetEnumValue.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\GetHotBehaviourInChildrenAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\base\Serialize\GameObjects.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\SetTag.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\GameObjectCompare.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\AnimationCallback.cs" />
    <Compile Include="Assets\GameScript\Runtime\base\Serialize\DockManagerPortrait.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetOwner.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmManager.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Material\SetTextureScale.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\GetSceneBuildIndex.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\FsmCreateDownloader.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\LoadScene.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\FloatSubtract.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\SetName.cs" />
    <Compile Include="Assets\GameScript\Runtime\sdk\HWAndroidInterface.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UIScrollRectSnap.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Quaternion\GetQuaternionMultipliedByVector.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigFragmentMap.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigWeaponUnlock.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformGetAnchorMinAndMax.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigTurnTable.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUIElement\SetGUITexture.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldOnValueChangeEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRAdapter\CoroutineAdapter.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\MergeNode\FsmDownloadDiffZip.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\EffectLevelController.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\launcher\PrivacyView.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\CameraBlur.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorCullingMode.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetSkipOnDrop.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\SetFsmArray.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetEventTarget.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\OutLight\PostEffectBase.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Camera\ClampOrthographicView.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\FsmResourceInit.cs" />
    <Compile Include="Assets\GameScript\Runtime\base\Serialize\LoginIcon.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Quaternion\QuaternionSlerp.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\utils\UtilsHttp.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenQuaternion.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\utils\UtilsUI.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldOnValueChangeEvent.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiScrollRectSetNormalizedPosition.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerInput\PlayerInputGetFloat.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Selectable\uGuiNavigationSetMode.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerTimelineEventTarget\PlayMakerTimelineEventTarget.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayRemoveAll.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\DrawDebugLine.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetVideoClip.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\FindGameObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ComponentAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRScprits\ILRUtility.cs" />
    <Compile Include="Assets\GameScript\Runtime\sdk\Framework4Unity.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRCore\ILRManager.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\ArtPostProcessChange.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetChild.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\FsmResourceManifest.cs" />
    <Compile Include="Assets\GameRes\Shaders\Effect\Artist_Effect\Effect_Post.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Character\ControllerMove.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\SetIntValue.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigSkill.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetUrl.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\gameEffect\CallInnerMethodAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Character\ControllerSettings.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\IsSleeping2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\DebugObject.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiScrollbarGetDirection.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerPreparedCompletedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\UnloadSceneAsynch.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutTextLabel.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\FishType.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\GenericAttributes\RequiredAttribute.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\SelectRandomVector3.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ScriptControl\CallMethod.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\network\WebGetRequest.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UIScrollRect.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\DrawTexture.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldSetCaretBlinkRate.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2Invert.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorSpeed.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RenderSettings\SetAmbientLight.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiRawImageSetTexture.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2HighPassFilter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\GetScale.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\DrawDebugRay.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\GMDataStore.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Application\SetScreenResolution.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnPointerDownEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\LightDirectionScript.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UnityObject\SetObjectValue.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\UICanvasGroupTweenAlphaAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldOnEndEditEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\ClampPosition.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\GUIButton.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorBool.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\FsmDownloadWebFiles.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\SetSpriteColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\SwipeGestureEvent.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnPointerClickEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerInput\PlayerInputUpdateActionBase.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Layout\uGuiLayoutElementSetValues.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmQuaternion.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\SetVector3Value.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ProceduralMaterial\SetProceduralVector3.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformGetAnchorMin.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2Lerp.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Time\GetSystemDateTime.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigAchievementCareer.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\FloatAddMultiple.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Effects\Flicker.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayInsert.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Components\PlayMakerUiIntValueChangedEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRScprits\JsonUtility.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\SetVector2Value.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\GetPosition2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\GetScenePath.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\Raycast.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Audio\AudioPlay.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ScriptControl\InvokeMethod.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\AnimatorStopRecording.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\WakeAllRigidBodies.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerInput\PlayerInputGetMoveVector.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiGraphicSetColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\GetNextOverlapPoint2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiImageSetRaycastTarget.cs" />
    <Compile Include="Assets\GameScript\Runtime\base\Serialize\GameEffectBossPanelRoundGain.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Camera\SetCameraCullingMask.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\AnimatorStartRecording.cs" />
    <Compile Include="Assets\GameScript\Runtime\Global.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\IntChanged.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\BaseActions\TweenPropertyBase.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UITouchEffect.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Selectable\uGuiExplicitNavigationGetProperties.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetCanSetDirectAudioVolume.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiScrollbarSetNumberOfSteps.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SendRandomEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigRebateTask.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutBeginCentered.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnSubmitEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Material\SetRandomMaterial.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\NavMeshAgentAnimatorSynchronizer.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\EaseRect.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Layout\UiLayoutElementSetValues.cs" />
    <Compile Include="Assets\ThirdPart\Unity-Logs-Viewer\Reporter\ReporterGUI.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\GetKeyDown.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3Multiply.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetTime.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldSetSelectionColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\MousePick2dEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\network\WebPostRequest.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\EventSystemExecuteEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Components\PlayMakerUiEndEditEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetLastEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoClip\VideoClipGetSize.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiSliderSetNormalizedValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Layout\UiLayoutElementGetValues.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\GetSceneUnLoadedEventData.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\RayCast2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorLayerName.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetAspectRatio.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\MoveGameObjectToScene.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRCore\StaticMethod\ILRStaticMethod.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Rect\SetRectValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ProceduralMaterial\SetProceduralFloat.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishNode.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\SetFloatValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldDeactivate.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\GUIElementHitTest.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\StopAnimation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\GetVelocity.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Gamepad\GamepadReadStickValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\BlendAnimation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\Assert.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetTimeSource.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\IntWrap.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\CharacterShaderRender\ManualUpdateBehaviour.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3Invert.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerStartedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\SetAnimationSpeed.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorPivot.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\SortingNode.cs" />
    <Compile Include="Assets\GameScript\Runtime\Huatuo\CLRManager.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\MergeNode\FsmQueryDiffData.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\Vector2RandomValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerPlay.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\ShadowAdapt.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Lights\SetLightCookie.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerPrefs\PlayerPrefsDeleteAll.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenInt.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Time\ScaleTime.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\CapturePoseAsAnimationClip.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\WingProperty.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmEnum.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\SmoothLookAt2d.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Selectable\uGuiGetBlockColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\GetRaycastHitInfo.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayResize.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\GetButtonDown.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetTargetMaterialProperty.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\DeviceShakeEvent.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker uGui\Proxies\PlayMakerUGuiDragEventsExecutionProxy.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\Translate.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigShop.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ScriptControl\StartCoroutine.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetTargetCamera.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\GetVectorLength.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiScrollbarSetValue.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Selectable\uGuiTransitionSetType.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetCanSetTime.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\GetSceneIsValid.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Layout\uGuiRadialLayoutSetProperties.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorInt.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiButtonArray.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerPrefs\PlayerPrefsSaveVariable.cs" />
    <Compile Include="Assets\PlayMaker\Actions\String\FormatString.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishColliderBox.cs" />
    <Compile Include="Assets\GameScript\Runtime\base\Serialize\LoopFlash.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Selectable\uGuiTransitionGetType.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Convert\ConvertSecondsToString.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetCurrentFrameIndex.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigExperssion.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\gameEffect\PortraitStrDetermineAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\SpriteShadow.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiTextGetText.cs" />
    <Compile Include="Assets\ThirdPart\Unity-Logs-Viewer\Reporter\MultiKeyDictionary.cs" />
    <Compile Include="Assets\PlayMaker\Actions\String\StringSplit.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Application\TakeScreenshot.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Trigonometry\GetAtan.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3AddXYZ.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\FsmResourceDone.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiToggleSetIsOn.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\GetVector2XY.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\bullet\BulletRandomRotate.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\launcher\SelectLanguage.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\InverseTransformPoint.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\DebugDrawShape.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\BoolAnyTrue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\StringCompare.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnInitializePotentialDragEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2SnapToGrid.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorIsParameterControlledByCurve.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\LitJson\Lexer.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmMaterial.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerPrepare.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\c\LibC.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Level\DontDestroyOnLoad.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmString.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorPlaybackTime.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiTextSetFont.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Canvas\uGuiCanvasScalerSetScaleFactor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenFade.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Movie\PlayMovieTexture.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RenderSettings\SetFogDensity.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigMiniGame.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\BaseClasses\AnimatorFrameUpdateSelector.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2MoveTowards.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\TouchGUIEvent.cs" />
    <Compile Include="Assets\GameRes\Shaders\Post Processing\PostProcessor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Trigonometry\GetAtan2FromVector2.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorFeetPivotActive.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\Comment.cs" />
    <Compile Include="Assets\GameScript\Runtime\sdk\AndroidInterface.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\FloatAdd.cs" />
    <Compile Include="Assets\GameScript\Runtime\extension\UnityEngine_RectTransform_Extension.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\SetSpriteSortingLayerById.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiGraphicGetColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetParent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenPosition.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\BaseActions\TweenExtensions.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Event Properties\Actions\GetEventProperties.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerPrefs\PlayerPrefsHasKey.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\LookAtDirection.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldSetPlaceHolder.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorGravityWeight.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\avatar\AvatarSyncAnimatorByServerTimeAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\ObjectPool.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SendEventToFsm.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\BaseLogAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3Normalize.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\__internal\FsmStateActionAdvanced.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\CameraBloom.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Audio\PlayRandomSound.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\LitJson\IJsonWrapper.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformSetAnchorMax.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmBool.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Canvas\UiCanvasGroupSetAlpha.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\AllowSceneActivation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Camera\GetMainCamera.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\FloatOperator.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\RandomBool.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Character\GetControllerHitInfo.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GotoPreviousState.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\TouchObjectEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishMask.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiImageSetSprite.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\Position.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorApplyRootMotion.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmRect.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiTextSetText.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\Effect\PerspectiveCamera.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerPrefs\PlayerPrefsGetString.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\FindOverlaps.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\AnimateFloatV2.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\GetTriggerInfo.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerInput\PlayerInputGetVector2.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\CommonTipsPointer.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiButtonOnClickEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnPointerUpEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\InverseTransformDirection.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiSliderSetWholeNumbers.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\AddForce2d.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\Trails\CircularBuffer.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnPointerUpEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmFloat.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiScrollbarGetValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenFloat.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetFrameRate.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiSliderSetMinMax.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\IntCompare.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\Effect\EffectRotate.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\GameViewCollision.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutBeginScrollView.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmVariable.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\CameraEffect.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Components\PlayMakerUiBoolValueChangedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorNextStateInfo.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\ObjectCompare.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldMoveCaretToTextStart.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoClip\VideoClipGetLength.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Mesh\GetVertexPosition.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\SetMouseCursor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\GetMouseButtonUp.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\utils\UtilsEncode.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformFlipLayoutAxes.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\GetSceneActivateChangedEventData.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\GetSceneLoadedEventData.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetCanSetTimeSource.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutBeginVertical.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetIsLooping.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetTransform.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Character\ControllerCrouch.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutEndScrollView.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\utils\UtilsSystem.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiSliderGetMinMax.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorDelta.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\FPS.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\GetSceneName.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Convert\ConvertBoolToFloat.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\GetSpriteColor.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\SetGlobalShadowOffset.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\SmoothLookAtDirection.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformContainsScreenPoint.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutRepeatButton.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigSeries.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\TranslatePosition.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Character\ControllerCheckHeight.cs" />
    <Compile Include="Assets\GameScript\Runtime\sdk\SDKCallback.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Application\GetScreenWidth.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerPrefs\PlayerPrefsSetInt.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiScrollbarSetNumberofSteps.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\DebugEnum.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformGetRect.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerInput\PlayerInputEnableAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\FsmStartGame.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutToolbar.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\GetButton.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayAdd.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiGetLastPointerEventDataInputButton.cs" />
    <Compile Include="Assets\GameScript\Runtime\sdk\OpenHarmonyInterface.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigDailyQuest.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutVerticalSlider.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Level\RestartLevel.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiSliderSetValue.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRCore\ILRBehaviour.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Selectable\uGuiExplicitNavigationSetProperties.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigDuanWuTask.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\LitJson\JsonWriter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Audio\AudioStop.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetRenderMode.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Quaternion\GetQuaternionEulerAngles.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\BaseActions\TweenVariableBase.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\DebugFloat.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnCancelEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\FloatSwitch.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiIsPointerOverUiObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\IgnoreEvents.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\NextFrameEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Camera\SetMainCamera.cs" />
    <Compile Include="Assets\PlayMaker\Extension\Scripts\JustAPixel\uGui\RadialLayout.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetChildNum.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\SetLayer.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\TouchEngine.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\CameraBloom1.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigCardSysInfo.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnEndDragEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorLayerWeight.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\MovePosition2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnScrollEvent.cs" />
    <Compile Include="Assets\ThirdPart\Unity-Logs-Viewer\Reporter\ReporterMessageReceiver.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutIntLabel.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\BaseFsmVariableIndexAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\GUIBox.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\CollisionEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\UnloadScene.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\ScaleGUI.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldGetCaretBlinkRate.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\GetMouseButtonDown.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\MergeNode\FsmMergeFiles.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldGetIsFocused.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\GetTouchCount.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\GetFsmArray.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayTransferValue.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\GenericAttributes\FsmVariableTypeAttribute.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRCore\StaticMethod\MonoStaticMethod.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Level\LoadLevel.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldGetSelectionColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiEventSystemExecuteEvent.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnBeginDragEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\SmoothFollowAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\IntSubtract.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayClear.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetLayer.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\SetParent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetPreviousStateName.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\ResourceServices.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Character\GetControllerVelocity.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\Const.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmInt.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\OutLight\OutLightFishMaks.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\AddRelativeForce2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\GetJointBreakInfo.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\GetSpeed2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\SetDrag.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetVideoClip.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\GetVector2Length.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorStabilizeFeet.cs" />
    <Compile Include="Assets\GameRes\Shaders\PBRV3\CustomDirectionalLight.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\UGuiGridLayoutSetPadding.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\CreateNewGameObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUIElement\SetGUIText.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\Downloader\StorageSpace.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorHumanScale.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3Operator.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3ClampMagnitude.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\DestroyObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\SetScale.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UILocalizeCoordinate.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiImageGetFillAmount.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\MergeScenes.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ProceduralMaterial\RebuildTextures.cs" />
    <Compile Include="Assets\GameScript\Runtime\sdk\SDK.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\GetNextRayCast2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\BaseFsmVariableAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\BoolOperator.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UI.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiEventSystemCurrentRayCastAll.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUIElement\SetGUITextureAlpha.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigAchievementActivity.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UISafeArea.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutBeginArea.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\GetNextOverlapCircle2d.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\DynamicBone\DynamicBone.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UnityObject\GetComponent.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishAdapter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\MouseLook.cs" />
    <Compile Include="Assets\GameScript\Runtime\UWATest\UWABridge.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\AddAnimationClip.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\RandomFloat.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\GetAngleToTarget.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformGetLocalRotation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2Multiply.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Gamepad\GamepadReadButtonValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayCompare.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\DebugLog.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\audio\FmodSound3D.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\DetectDeviceOrientation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\GetSceneProperties.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\IntClamp.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ActionHelpers.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetEventVector2Data.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoClip\VideoClipGetFrameRate.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Material\SetVisibility.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\SimpleLook.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Character\ControllerJump.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmFloat.cs" />
    <Compile Include="Assets\PlayMaker\Actions\String\StringJoin.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\IsKinematic2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\SendActiveSceneChangedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetIsPlaying.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Convert\ConvertVector2ToVector3.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Movie\MovieTextureAudioSettings.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\gameEffect\CarrierBallPanelAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetNextChild.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetCanStep.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\DevicePlayFullScreenMovie.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Material\GetMaterial.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetRenderMode.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetEventIntData.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiSliderGetMinMax.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetTexture.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\ParseJsonValueCountAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2Add.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Quaternion\QuaternionAngleAxis.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorIsLayerInTransition.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\GetSceneRootGameObjects.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishViewNormal.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Selectable\UiExplicitNavigationSetProperties.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetTargetMaterialProperty.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\OutLight\CameraEffect.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishView.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiButtonOnClickEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetTag.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayRemove.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRCore\AdaptMethod.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\GameObjectHasChildren.cs" />
    <Compile Include="Assets\PlayMaker\Actions\String\SelectRandomString.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\DebugGameObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Time\Wait.cs" />
    <Compile Include="Assets\GameScript\Runtime\base\Serialize\GameRewardItem.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\LitJson\JsonData.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldMoveCaretToTextEnd.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\MergeNode\FsmDownloadDiffConfig.cs" />
    <Compile Include="Assets\PlayMaker\Actions\String\SetStringValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\ForwardEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\Effect\ImageGlow.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\FsmArraySet.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutBeginAreaFollowObject.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Canvas\uGuiCanvasGroupSetProperties.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\RewindAnimation.cs" />
    <Compile Include="Assets\Temporary\BossTest\CaveExplorationTest\MapGenerator.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Gamepad\GamepadGetButtonValues.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigMerryChristmasRanking.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\BaseActions\TweenHelpers.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2Substract.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerEvent\PlayMakerEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\Sleep.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Selectable\UiTransitionSetType.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiGraphicGetColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\SetPosition.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnBeginDragEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRScprits\MonoBehaviourEvent.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PlayMakerUtils_Fsm.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformSetAnchorRectPosition.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3PerSecond.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetTargetTexture.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayGet.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\DeviceOrientationEvent.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PlayMakerUtils_FsmVar.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\MergeNode\FsmMergeDone.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\RandomInt.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldSetText.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\GameLogger.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutBeginHorizontal.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerInput\PlayerInputButtonEvents.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigStore.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UISkewText.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Character\ControllerSimpleMove.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\EnableFSM.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerPrefs\PlayerPrefsLoadVariable.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\TriggerEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animation\BaseAnimationAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\AnimatorSetStateZero.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\ButtonEffect.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldOnSubmitEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiSliderOnValueChangedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\GetMouseButton.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\IsSleeping.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\AnimateColor.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\DynamicBone\DynamicBoneCollider.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorCurrentTransitionInfoIsName.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenVector2.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\FindChild.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutToggle.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnDragEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigItemNew.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\EaseColor.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\BloodSlider.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldGetText.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetEventSentBy.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\SetIsKinematic.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\Effect\CameraEffectsBase.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnPointerClickEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UIEventPost.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3RotateTowards.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetTime.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PlayMakerUtils_conversions.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UITagGroup.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\GetLastPointerEventDataInputButton.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3Lerp.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\ResourceWindow.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\Canvas\uGuiCanvasForceUpdateCanvases.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiTextGetText.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformSetAnchoredPosition.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\SetControllerVelocity.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\SetPosition2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutFloatLabel.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\gameEffect\GetInnerPropertyAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UIToggleGroup.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiRebuild.cs" />
    <Compile Include="Assets\GameScript\Runtime\sdk\RoleData.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Rect\RectOverlaps.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\utils\Utils.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetUrl.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerInput\PlayerInputGetVector2AsVector3.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigNewbieTask.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Lights\SetLightRange.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerLoopPointReachedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorApplyRootMotion.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Color\ColorInterpolate.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiGetSelectedGameObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorLookAt.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishController.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiOnSubmitEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoClip\VideoClipGetAudioTrackCount.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\GenericAttributes\EventTargetVariableAttribute.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\network\WebRequestBase.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SyncAnimatorBoolToState.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Selectable\UiGetBlockColor.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker uGui\Proxies\PlayMakerUGuiDropEventsProxy.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnScrollEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\PlayerInput\PlayerInputEnableActionMap.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\EaseFloat.cs" />
    <Compile Include="Assets\GameScript\Runtime\sdk\IOSInterface.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\GetTouchInfo.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\LoadPrefabAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Audio\SetGameVolume.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UITag.cs" />
    <Compile Include="Assets\PlayMaker\Extension\Scripts\uGui\BoxCollider2dMatchRectTransform.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3HighPassFilter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformGetOffsetMax.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\Vector3LowPassFilter.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldGetCharacterLimit.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformSetLocalRotation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\AlignToDirection.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmTexture.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\action\GAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\FloatCompare.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\GetButtonUp.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\DataStore.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Color\GetColorRGBA.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Camera\SetCameraFOV.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigCurrency.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiDropDownGetSelectedData.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\LitJson\JsonReader.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigMoneyTree.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\ActivityTipsPointer.cs" />
    <Compile Include="Assets\GameScript\Runtime\base\GuidePostEventCommon.cs" />
    <Compile Include="Assets\PlayMaker\Actions\String\BuildString.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetDistanceXYZ.cs" />
    <Compile Include="Assets\GameScript\Runtime\extension\System_String_Extension.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorBoneGameObject.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigILR.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArraySet.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\SmoothLookAt.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\StartLocationServiceUpdates.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\AnimateFloat.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\ActivateSolo.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\MessageBox.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSeekCompletedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Material\SetMaterialFloat.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\AnimatorStopPlayback.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldGetText.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Components\PlayMakerUiClickEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigCannonSpeciality.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\RunFSM.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker uGui\Proxies\PlayMakerUGuiComponentProxy.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\DynamicBone\DynamicBoneColliderBase.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\SetVelocity.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\SetGameObject.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\SetJsonStrToStore.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\DrawFullscreenColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformSetAnchorMin.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector3\SetVector3XYZ.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\bullet\BulletNet.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Canvas\UiCanvasGroupSetProperties.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerEventTarget\PlayMakerEventTarget.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Convert\ConvertBoolToColor.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiOnMoveEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Lights\SetShadowStrength.cs" />
    <Compile Include="Assets\GameScript\Runtime\art\MaterialLevelController.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorLeftFootBottomHeight.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\Timers.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\TMPDynamicText.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\UseGravity.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\GUILabel.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArraySort.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutConfirmPasswordField.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\GetMouseY.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiSliderGetValue.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\GenericAttributes\ShowOptionsAttribute.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldSetAsterixChar.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiDropDownAddOptions.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Audio\PlaySound.cs" />
    <Compile Include="Assets\GameScript\Runtime\lib\action\GActionInterval.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\GetSceneCountInBuildSettings.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetRoot.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\GetLastPointerDataInfo.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiDropDownSetValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerStepForward.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\boot\BootBackground.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigGuide.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Quaternion\QuaternionCompare.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Selectable\UiNavigationSetMode.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigMonopoly.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldGetCharacterLimit.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UnityObject\GetProperty.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ProceduralMaterial\SetProceduralVector2.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerFrameReadyEvent.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiGraphicSetColor.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\GetChildWithIndexAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutFloatField.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\Collision2dEvent.cs" />
    <Compile Include="Assets\GameScript\Runtime\base\CanvasAdapter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\MousePick.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiToggleOnValueChangedEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmVector3.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorTarget.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Audio\SetAudioClip.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorCurrentTransitionInfoIsUserName.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetSendFrameReadyEvents.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldSetHideMobileInput.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\LitJson\JsonException.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\GetNextOverlapArea2d.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorIsMatchingTarget.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\Downloader\FileDownloader.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiScrollRectSetVertical.cs" />
    <Compile Include="Assets\GameScript\Runtime\extension\UnityEngine_Object_Extension.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Material\SetMaterial.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerStop.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\EaseFsmAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\BoolAllTrue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiSliderSetValue.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigCannonMethod.cs" />
    <Compile Include="Assets\GameScript\Runtime\utility\utils\UtilsTime.cs" />
    <Compile Include="Assets\PlayMaker\Actions\ScriptControl\CallStaticMethod.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\CameraCustomShakeAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformSetOffsetMin.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishAnchor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\Loop.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Rect\GetRectFields.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformGetSizeDelta.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\GuideClickButton.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\CurveColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformGetLocalPosition.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetFsmQuaternion.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\FsmPrepare.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiSliderGetDirection.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\SelectRandomGameObject.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigBlendShaderInfo.cs" />
    <Compile Include="Assets\Temporary\BossTest\CaveExplorationTest\RewardManager.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformSetScreenRectFromPoints.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Audio\SetAudioPitch.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishCollider.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\fish\FishAnchorGroup.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\TriggerEventAction.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\EventSystemCurrentRayCastAll.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Time\GetTimeInfo.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Lights\SetLightFlare.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\Canvas\UiCanvasScalerSetScaleFactor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Level\LoadLevelNum.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiScrollRectGoToItem.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiSliderGetNormalizedValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\TransformPoint.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetEventFloatData.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\AnimateFsmAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\AnyKey.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorTrigger.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Input\TransformInputToWorldSpace.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\GetFsmGameObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Quaternion\QuaternionLowPassFilter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\FindClosest.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetDistance.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UISpineAdaptLoading.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiGraphicCrossFadeAlpha.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiToggleSetIsOn.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\BoolTest.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\SetVector2XY.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\serialize\Curve.cs" />
    <Compile Include="Assets\PlayMaker\Actions\RectTransform\RectTransformGetOffsetMin.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Array\ArrayGetRandom.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigMerryChristmasTask.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UITagTrigger.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputfieldSetCaretBlinkRate.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\CallCLRMethodAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiInputFieldSetPlaceHolder.cs" />
    <Compile Include="Assets\GameScript\Runtime\logic\game\GameViewGenerator.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\ColorCompare.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigItemBox.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\GetAnimatorIKGoal.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\GameObjectCompareTag.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiScrollbarSetSize.cs" />
    <Compile Include="Assets\GameScript\Runtime\ui\UIScrollView.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Material\SetMaterialValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Trigonometry\GetTan.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SpriteRenderer\GetSpriteSortingLayer.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRAdapter\IIComparerFloat.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\FloatDeltaAngle.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Utils\Actions\MonoBehaviour\TransformEventsBridge.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Convert\ConvertFloatToInt.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Material\SetTextureValue.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\gameEffect\PlayInnerAudioAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics\RaycastAll.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutIntField.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Lights\SetLightSpotAngle.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoClip\VideoClipGetOriginalPath.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetSendFrameReadyEvents.cs" />
    <Compile Include="Assets\PlayMaker\FsmProcessor.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\uGuiSetSelectedGameObject.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\avatar\AvatarSetTargetMarkPosAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigItemFragment.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\SetEventStringData.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldSetCharacterLimit.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\EventSystem\IsPointerOverUiObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\StateMachine\ForwardAllEvents.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Quaternion\QuaternionLookRotation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Lights\SetLightType.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\FsmNode\MergeNode\FsmImportFiles.cs" />
    <Compile Include="Assets\GameScript\Runtime\base\Serialize\OutlineBreath.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\SetRotation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\BoolChanged.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\serialize\Floats.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiGraphicCrossFadeAlpha.cs" />
    <Compile Include="Assets\PlayMaker\Actions\String\StringReplace.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Material\GetMaterialTexture.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\FloatClamp.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\Transform\SetTransformParent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\UiSetSelectedGameObject.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\DeactivateSelf.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\SetGUIColor.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiScrollbarSetValue.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Material\SetMaterialColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetTargetCamera.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Color\SelectRandomColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Device\TouchEvent.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Effects\Blink.cs" />
    <Compile Include="Assets\GameScript\Runtime\updater\ResMessageBoxMgr.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Vector2\Vector2Operator.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUI\SetGUIContentColor.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Application\ApplicationQuit.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUILayout\GUILayoutEndArea.cs" />
    <Compile Include="Assets\PlayMaker\Actions\String\GetSubstring.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\UiScrollRectSetHorizontal.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Transform\SetRandomRotation.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\FloatWrap.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigWeaponForge.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Math\SelectRandomInt.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\LitJson\JsonMapper.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldGetHideMobileInput.cs" />
    <Compile Include="Assets\PlayMaker\Actions\AnimateVariables\AnimateRect.cs" />
    <Compile Include="Assets\GameScript\Runtime\ILRuntime\ILRAdapter\IAsyncStateMachineAdapter.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Animator\SetAnimatorSpeed.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Logic\StringSwitch.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldScreenToLocal.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerGetIsPrepared.cs" />
    <Compile Include="Assets\PlayMaker\Actions\VideoPlayer\VideoPlayerSetAudioOutputMode.cs" />
    <Compile Include="Assets\PlayMaker\Actions\SceneManager\GetSceneIsLoaded.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\table\ConfigBossIdMap.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenCamera.cs" />
    <Compile Include="Assets\PlayMaker\Extension\PlayMaker Custom Actions\uGui\uGuiInputFieldDeactivate.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GameObject\GetRandomChild.cs" />
    <Compile Include="Assets\PlayMaker\Actions\UI\EventSystem\EventTriggerActionBase.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\BaseActions\TweenEnums.cs" />
    <Compile Include="Assets\PlayMaker\Actions\GUIElement\SetGUITextureColor.cs" />
    <Compile Include="Assets\GameScript\Runtime\extension\UnityEngine_Transform_Extension.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Physics2D\WakeUp2d.cs" />
    <Compile Include="Assets\GameScript\Runtime\config\serialize\Sprites.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\BaseActions\TweenComponentBase.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Tween\TweenVector3.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\PlayAudioAction.cs" />
    <Compile Include="Assets\PlayMaker\Actions\Debug\DebugInt.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\GActionPlayMaker.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\GActionRepeatFsmAction.cs" />
    <Compile Include="Assets\GameScript\Runtime\playmaker\common\TestAction.cs" />
    <None Include="Assets\GameRes\Config\ct\ct_sds.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_boss_show.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_2\caishen_2.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\NPR\ShaderLibrary\AvatarLighting.cginc" />
    <None Include="Assets\Fishes\Wings\PrefabArt\wings_3999\spine\wing_11.json" />
    <None Include="Assets\[废弃]\juxie_dlw\spine\juxie_dlw.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_0101.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_yaogunnangua.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\HairOpaque_02.shader" />
    <None Include="Assets\GameSetting\AssetArtScannerConfig.json" />
    <None Include="Assets\GameRes\Config\ct\ct_chrismas_tips.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_02_portrait.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28013\laohu.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\Fish2DShader.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_fuxing.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_Disturbance_Additve.shader" />
    <None Include="Assets\GameRes\Config\tracks\bignian_01_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_wing.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_weapon.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_meirenyu02.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_xiaochou_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Effect_cl_kuosan.shader" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMPro.cginc" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\shoushi\bixin\bixin.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\artifact_coming\spine_houzi\houzi.json" />
    <None Include="Assets\GameRes\Config\ct\ct_gift_mall_gift_C.txt" />
    <None Include="Assets\GameSetting\AssetBundleCollectorConfigTest.xml" />
    <None Include="Assets\GameRes\Config\tracks\boss_eyu_nuanchang.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_19_portrait.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_3\caishen_3.json" />
    <None Include="Assets\GameRes\Shaders\Guide\GuideRectMask.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_achievement_career_new.txt" />
    <None Include="Assets\GameRes\Shaders\Hair\CGIncludes\UnityAnisotropicShadow.cginc" />
    <None Include="Assets\GameRes\Config\ct\ct_1701.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28002\room_28002.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_sds_tianchengzuo.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_sds_shizizuo.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28002\room_28002.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\artifact\spine_tangseng\tangseng.json" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_Basic_Glass.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_store3.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\dw\spine_duanwei_qizi\duanwei_qizi.json" />
    <None Include="Assets\GameRes\Config\tracks\group_405.txt" />
    <None Include="Assets\GameRes\Config\tracks\xianshi_xiaochou.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_personal_method.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28007_baihu\room_28007.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_8\dijing_8.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28005\room_28005.json" />
    <None Include="Assets\GameRes\Config\tracks\special_zuantou01_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_gift_month_old.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9204_4.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\season_equipment\spine_jijialongxi_2\jijialongxi_2.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\LeafSwing.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_3\dijing_3.json" />
    <None Include="Assets\GameRes\Shaders\Effect\Artist_Effect\Effect_ScreenInvert.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\shoushi\touxiang\touxiang.atlas.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\artifact\spine_jixianfeng\jijianfeng.json" />
    <None Include="Assets\GameRes\Config\ct\ct_zhenbaoge_sell_item.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_cannon_combo.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_05_portrait.txt" />
    <None Include="Assets\PlayMaker\Readme.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\FishStandard.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_sg_simayi.txt" />
    <None Include="Assets\Fishes\Cannon\PrefabArt\109\spine\cannon_109.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28013_1\tianshi.atlas.txt" />
    <None Include="Assets\Fishes\Wings\PrefabArt\wings_3901\spine\wing_feather.atlas.txt" />
    <None Include="Assets\TextMesh Pro\Preset\FontSource\hanzi_basic.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_language_key.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_xiaoyouximenpiao.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_4\dijing_4.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\season_equipment\spine_jijialongxi_1\jijialongxi_1.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\PBR\CustomGlobalIllumination.cginc" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28001_meirenyu\room_28001.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_Dissolve_Additive.shader" />
    <None Include="Assets\MiniGamesRes\common\PrefabArt\mini_common\spine\login\shark.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_lucky_turntable.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_6\joker_6.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_dailytask_daily.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28009\tangseng.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_xueseng_nuanchang.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_4\caishen_4.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_3\dijing_3.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_tianchengzuo.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_mds_minuosi.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_fuxing_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_nezha_nuanchang.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_tiesuoelong_nuanchang.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_hm_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_longhunzhu.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_lvbu.txt" />
    <None Include="Assets\GameRes\Shaders\Hair\CGIncludes\UnityAnisotropicMeta.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_huohu.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_xingyunzixuan.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV2\CustomPBSLighting.cginc" />
    <None Include="Assets\GameRes\Config\ct\ct_zhenbaoge_item_artfifact_adapte.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_3\joker_3.atlas.txt" />
    <None Include="Assets\Fishes\FishBoss\PrefabArt\1612\Spine\panlong.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_xiaomonv.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_3zhounian_unfreeze.txt" />
    <None Include="Assets\PlayMaker\Extension\PlayMaker uGui\VersionInfo.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28011\jijiasanguo.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_ss_xuanwu.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_achievement_activity_new.txt" />
    <None Include="Assets\[废弃]\TA_Test\yuyang\PBRV4\ToonShader2.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_endless_treasures_reward.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Effect_Dissolve_Particle.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_jijia_bawanglong_plus.txt" />
    <None Include="Assets\GameRes\Config\tracks\bignian_01_unfreeze.txt" />
    <None Include="Assets\GameRes\Shaders\Particle Add Intensify.shader" />
    <None Include="Assets\GameRes\Shaders\PBR\CustomStandardBRDF.cginc" />
    <None Include="Assets\GameRes\Config\tracks\lamp_10_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_0001.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_7\joker_7.json" />
    <None Include="Assets\GameRes\Config\json\cj_spring_festival_exchange.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_eyu_chaoxiao.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Artist_Effect\Effect_DIssolve_Additive.shader" />
    <None Include="Assets\GameRes\Shaders\PBRV3\OutlineEfectTransparent.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\login_cg\spine_loading2\loading2.json" />
    <None Include="Assets\TextMesh Pro\Preset\FontSource\dynamic.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_achievement_rewards_new.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_rulaishenzhang.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_0204.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_crab_sbomb.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_taikongmao_xiao_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_mds_hadisi.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_sds_jinniuzuo.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_guide.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9201_1.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_fish.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_taiqiunvlang_zhong.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_achievement_fish.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28011_2\zhugeliang2.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_sds_sheshouzuo.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\FurTransparentHig.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\artifact\spine_tangseng\tangseng.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\PBR\CustomPBSLighting.cginc" />
    <None Include="Assets\GameRes\Config\tracks\lamp_07_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_caishen_portrait.txt" />
    <None Include="Assets\[废弃]\TA_Test\yunxiang\PoSui_VAT\Shader\Vat_test2.shader" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_ParticleShader.shader" />
    <None Include="Assets\GameRes\Config\tracks\group_0203.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\FurHelper.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_daobaodijing.txt" />
    <None Include="Assets\GameRes\Shaders\NPR\ShaderLibrary\CartoonZOffset.cginc" />
    <None Include="Assets\GameRes\Config\ct\ct_personal_info_path.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\xiyou\spine_sunwukong\sunwukong.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_nangua2024.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_lottery_union.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_baixiang.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\bargain_gift\spine\shanzi.json" />
    <None Include="Assets\GameRes\Config\tracks\group_guide_goldfish_infinite_treasure.txt" />
    <None Include="Assets\Fishes\Cannon\PrefabArt\109\spine\cannon_109.json" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Bitmap.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_festa_festival_2024.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_sunwukong.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jijia_daoshou_inferior.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetTexture\Texture Scanner_展示.json" />
    <None Include="Assets\GameRes\Config\json\cj_vip_privilege.json" />
    <None Include="Assets\Fishes\Cannon\PrefabArt\2D普通炮台的美术资源.txt" />
    <None Include="Assets\GameRes\Config\json\cj_a.json" />
    <None Include="Assets\GameRes\Config\ct\ct_longka.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_skill.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_dock_lobby_left_seq.txt" />
    <None Include="Assets\GameRes\Shaders\Hair\CGIncludes\UnityTranslucentLighting.cginc" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28013\laohu_effect.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9203_3_portrait.txt" />
    <None Include="Assets\GameRes\Config\json\cj_season_equipment_r1_v1.json" />
    <None Include="Assets\GameRes\Config\ct\ct_season_equipment_innate_level.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_ss_qinglong.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\FishCgincBase.cginc" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\login_cg\spine_login_shenshou\shenshou_login.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_sds_xianding_gift.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_10.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_13.txt" />
    <None Include="Assets\GameRes\Config\json\cj_boss_1704.json" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_level_old.txt" />
    <None Include="Assets\GameRes\Config\tracks\summon_06_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\CircleMask.shader" />
    <None Include="Assets\Fishes\CannonSkin\PrefabArt\战神的美术资源.txt" />
    <None Include="Assets\GameRes\Config\tracks\shijian_01_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_meirenyu_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Effect_emission.shader" />
    <None Include="Assets\GameRes\Config\tracks\lamp_04_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_Unique_Ice.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_baiyangzuo.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_recording.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_dock_game_right_seq_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_Basic_Additive.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28013\laohu.json" />
    <None Include="Assets\GameRes\Shaders\Effect\CustomShuipingzuo.shader" />
    <None Include="Assets\GameRes\Shaders\Hair\CGIncludes\UnityAnisotropicCoreForward.cginc" />
    <None Include="Assets\GameRes\Config\tracks\lamp_16.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_1\joker_1.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_gift_month.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_2024_happy_summer_pass.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_04.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetTexture\Texture Scanner_TA.json" />
    <None Include="Assets\GameRes\Config\tracks\group_0202.txt" />
    <None Include="Assets\GameRes\Config\tracks\tide_yuqun05.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_12_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\Video\UIVideo.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_ss_baihu.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28011_1\zhugeliang.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_sds_juxiezuo.txt" />
    <None Include="Assets\GameRes\Config\json\cj_anniversary2nd.json" />
    <None Include="Assets\GameRes\Config\ct\ct_shop.txt" />
    <None Include="Assets\GameRes\Config\tracks\xianshi_ChristmasBag.txt" />
    <None Include="Assets\GameSetting\link.xml" />
    <None Include="Assets\GameRes\Config\tracks\group_guide_goldfish_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_dailyquest_reward.txt" />
    <None Include="Assets\PlayMaker\Templates\Readme.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_2\joker_2.json" />
    <None Include="Assets\GameRes\Shaders\PBRV2\CustomStandardShadow.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_jinjiao_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_nvwa.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_4\joker_4.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\artifact\spine_liumangxing\liumangxing.json" />
    <None Include="Assets\GameRes\Shaders\Effect\Artist_Effect\ab_RadialBlur.shader" />
    <None Include="Assets\GameRes\Shaders\Effect\Artist_Effect\Effect_MoHu+SeSan.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_vip_privilege.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_03_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_endless_treasures_repair.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\G_dissolve_blend2.shader" />
    <None Include="Assets\GameRes\Config\tracks\summon_03_portrait.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_7\dijing_7.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_02.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_5\caishen_5.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_404.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jiangtaigong.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9201_2.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_ss_zhuque.txt" />
    <None Include="Assets\GameRes\Config\json\cj_season_equipment_r2_v4.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_nvwa_nuanchang.txt" />
    <None Include="Assets\GameRes\Shaders\Post Processing\Common\Shaders\BlurShader.shader" />
    <None Include="Assets\GameRes\Config\tracks\lamp_20.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jixiesha02.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_dragon_wish.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_ss_xuanwu_sheng.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_5\joker_5.atlas.txt" />
    <None Include="Assets\TextMesh Pro\Preset\FontSource\hanzi_all.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_emoji.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_Mask_AlphaBlend.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_jinniuzuo.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_xueseng_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_zhulong_nuanchang.txt" />
    <None Include="Assets\GameRes\Config\tracks\tide_yuqun03.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_jinchan.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\season_equipment\spine_jijialongxi_1\jijialongxi_1.json" />
    <None Include="Assets\GameSetting\ScanReport\AssetMaterial\Material Scanner_特效.json" />
    <None Include="Assets\GameRes\Config\ct\ct_discount_conch.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetMaterial\Material Scanner_共用.json" />
    <None Include="Assets\GameRes\Config\tracks\tide_yuqun04.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_zhadan_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\summon_04_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_dock_game_left_seq_portrait.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetMaterial\Material Scanner_Boss.json" />
    <None Include="Assets\GameSetting\ScanReport\AssetMaterial\Material Scanner_无引用.json" />
    <None Include="Assets\GameRes\Shaders\PBRV2\CustomStandardMeta.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_longgui02.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_denglongyu.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_fuxing_gift.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetPrefab\Prefab Scanner_战神.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_jiulianbaodeng.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_competition_reward.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_grow_help_new.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9204_1.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9204_3.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28013_tianshi\tianshi.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_personal_title.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jinlongbaoshu.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_zhenbaoge_item_banner.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_zhulong_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\Skin_FastSSS_Middle.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_sds_chunvzuo.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_11_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_moyuan_nuanchang.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_mojiezuo.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_0302.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28009\tangseng.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_09_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\UI\Particle-AlphaMask.shader" />
    <None Include="Assets\GameRes\Shaders\PBRV2\CustomStandardCore.cginc" />
    <None Include="Assets\GameRes\Config\ct\ct_legend_task.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_6\caishen_6.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_achievement_jump.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_rebate_task.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_fenghuang_nuanchang.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_tslm_chitianshi.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_tiesuoelong.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_08_portrait.txt" />
    <None Include="Assets\PlayMaker\Extension\PlayMaker Utils\VersionInfo.json" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_material_info.txt" />
    <None Include="Assets\GameRes\Shaders\WaterWave.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_huangzhong.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_tieshangongzhu.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28008\room_28008_effect.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_eyu02.txt" />
    <None Include="Assets\GameRes\Shaders\UI\Particle-AddMask.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_taiqiunvlang.txt" />
    <None Include="Assets\GameRes\Shaders\Guide\GuideCircleMask.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_huolong.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_dailytask_lottery.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9203_3.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_yinjiao.txt" />
    <None Include="Assets\GameRes\Shaders\Post Processing\Common\Shaders\LUTShader.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_gift_mall_tab_B.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lion\spine_shizi\shizi.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_6\joker_6.json" />
    <None Include="Assets\GameRes\Config\ct\ct_boss_coming_new.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_sg_caocao.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_4\dijing_4.json" />
    <None Include="Assets\GameRes\Config\ct\ct_weapon_unlock.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetFBX\FBX Scanner_特效.json" />
    <None Include="Assets\GameRes\Shaders\Effect\Sanguo_nq_uv.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_anniversary_game.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_luxing.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\spring_festival_2025\spine\shengtianshi\shengtianshi.json" />
    <None Include="Assets\GameRes\Shaders\Effect\Artist_Effect\CutInMask(RGB).shader" />
    <None Include="Assets\GameRes\Config\tracks\group_401.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_crab_drill_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\Skin_FastSSS_02.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\bargain_gift\spine\shanzi.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_gift_paid.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_xueseng.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_DD_AlphaBlend.shader" />
    <None Include="Assets\GameRes\Config\tracks\string_9202_1_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_quest.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_lvbu_inferior.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\super_star_cunchu\spine\cqg_jinzhu.atlas.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetFBX\FBX Scanner_3D炮台.json" />
    <None Include="Assets\GameSetting\ScanReport\AssetTexture\Texture Scanner_3D炮台.json" />
    <None Include="Assets\GameRes\Shaders\Galaxy.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_huohu_nuanchang.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\bargain_gift\spine\mao.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_game_music.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_zuantou01.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_likui.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_13_portrait.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28003_erlong\room_28003.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_change_xiao_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\PBR\CustomStandardInput.cginc" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_7\dijing_7.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\zhanshenxunmeng\spine_taishanglaojun\taishanglaojun.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_catch_boss_broadcast.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetTexture\Texture Scanner_战宠.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_3\joker_3.json" />
    <None Include="Assets\GameRes\Shaders\NPR\ShaderLibrary\AvatarBackFacingOutline.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_xy_moliqing.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_cannon_effect.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_ss_zhuque_sheng.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_zhadan.txt" />
    <None Include="Assets\GameRes\Shaders\Post Processing\Common\Shaders\BloomFilterShader.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_gift_mall_tab.txt" />
    <None Include="Assets\GameRes\Shaders\TechY\FishMask.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_shop_shengtianshi.txt" />
    <None Include="Assets\GameRes\Config\json\cj_public.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_dapeng.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\FishStandard_Low.shader" />
    <None Include="Assets\GameRes\Config\tracks\string_9202_4_portrait.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\forge\spine\xuchu.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_scene.txt" />
    <None Include="Assets\GameRes\Config\tracks\xianshi_ChristmasBag_portrait.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28011\jijiasanguo.json" />
    <None Include="Assets\GameRes\Config\ct\ct_gift_mall_hot_source.txt" />
    <None Include="Assets\GameRes\Shaders\Effect_distortion_blend.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_timematch_reward.txt" />
    <None Include="Assets\GameRes\Config\json\cj_activity.json" />
    <None Include="Assets\GameRes\Config\tracks\lamp_12.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_artifact_privilege.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Effect_Dissolve_Disturbance_AlphaBlend.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_longwang.txt" />
    <None Include="Assets\GameRes\Config\tracks\tide_yuqun03_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\particle_distort.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_zhulong.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28007_baihu\room_28007.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_jijia_bawanglong.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jinlongbaoshu2024.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\FishCgincBase_Test.cginc" />
    <None Include="Assets\GameRes\Config\ct\ct_music_festival_2023.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_item.txt" />
    <None Include="Assets\Fishes\CannonSkin\Prefab\动态下载皮肤.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\shoushi\dianzan\dianzan.json" />
    <None Include="Assets\GameRes\Shaders\Hair\CGIncludes\UnityAnisotropicInput.cginc" />
    <None Include="Assets\GameSetting\BuildReport_FishPackage.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\zhanshenxunmeng\spine_yangjian\yangjian.json" />
    <None Include="Assets\GameRes\Shaders\Effect\lc_rongjie.shader" />
    <None Include="Assets\GameRes\Config\tracks\lamp_03.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_08.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetTexture\Texture Scanner_场景.json" />
    <None Include="Assets\ThirdPart\Proxima\package.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_5\dijing_5.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_jijia_pangxie.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_fragment_map.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28003\room_28003.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_14.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\dock\spine_share_icon_moment\share_icon_moment.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_ss_xuanwu_zhong.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_festa_festival_25_02_pass.txt" />
    <None Include="Assets\Fishes\Wings\PrefabArt\wings_3904\spine\wing_phoenix.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28008\room_28008_effect.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_xuchu.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Fresnel_Add_Zwrite.shader" />
    <None Include="Assets\GameRes\Config\tracks\special_zuantou_portrait.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetMaterial\Material Scanner_多玩法共用.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\login_cg\spine_login_shenshou\shenshou_login2.json" />
    <None Include="Assets\GameRes\Config\ct\ct_store.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_rulaishenzhang_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\teshu01.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28011_2\zhugeliang2.atlas.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets\Fishes\Wings\PrefabArt\wings_3903\spine\wing_butterfly.atlas.txt" />
    <None Include="Assets\UWA\UWA_SDK\package.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_xiankun_nuanchang.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_skin_ae_group.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV2\CustomStandardInput.cginc" />
    <None Include="Assets\GameRes\Config\tracks\string_9203_1.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_boss_base_effect.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9202_4.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\DisappareParticleAdd.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_boss_1plus1.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\jianbaodashi\spine_ren_kanbanniang\ren_kanbanniang.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_hm.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_huolong_inferior.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_402.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_0201_infinite_treasure.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_xiahoudun.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_8\joker_8.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_jixiekuangsha.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_newbie_task.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_global_privilege.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\xiyou\spine_sunwukong\sunwukong.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_blend_shader.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\zhanshen_lottery\spine\jiaxu.atlas.txt" />
    <None Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\README.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_weapon_forge.txt" />
    <None Include="Assets\GameRes\Shaders\NPR\ShaderLibrary\AvatarGenshinInput.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_suoyaota.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_5\dijing_5.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_xiankun.txt" />
    <None Include="Assets\GameRes\Shaders\UI\UI-LightBurst.shader" />
    <None Include="Assets\GameRes\Shaders\PBRV3\FishStandard_Cloth.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28003_erlong\room_28003.atlas.txt" />
    <None Include="Assets\GameSetting\ScanReport\Reference\Reference Scanner_Fish.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_shuipingzuo.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28005_molong\room_28005.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Effect_ModelAlphaBlend.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\bargain_gift\spine\mao.json" />
    <None Include="Assets\GameRes\Config\tracks\lamp_14_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\summon_03.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\wave_blend.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_notices_boss_show.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Artist_Effect\Effect_black_white.shader" />
    <None Include="Assets\GameRes\Shaders\liuguang.shader" />
    <None Include="Assets\GameRes\Config\tracks\string_9202_2.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_18_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_zhadan01.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Artist_Effect\Effect_Post.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_jinlongbaoshu_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_vip_item_mark.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_crab_sbomb_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\Hair_Geometry.shader" />
    <None Include="Assets\GameRes\Config\tracks\summon_01_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_gongfuzuqiu.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Scroll2Tex.shader" />
    <None Include="Assets\GameRes\Shaders\Effect\UI-EffectText.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_season_equipment_innate.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\artifact\spine_pilihuo\pilihuochaifen_spine.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_sds_shuangyuzuo.txt" />
    <None Include="Assets\GameRes\Config\tracks\teshu01_portrait.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28005_molong\room_28005.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\double_eleven\spine_shixiang\shixiang.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28013_1\tianshi.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_zhulong02.txt" />
    <None Include="Assets\Fishes\Wings\PrefabArt\wings_3904\spine\wing_phoenix.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\FishStandard_Base_Test.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_mini_game2.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_collect_hall.txt" />
    <None Include="Assets\ThirdPart\Proxima\Third-Party Notices.txt" />
    <None Include="Assets\Resources\BootLang.txt" />
    <None Include="Assets\GameRes\Config\tracks\xianshi_ruyidai.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_xy_laojun_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_xishen.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_sheshouzuo.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\login_cg\spine_loading2\loading2.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\xianshi_dangaoyu_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_shijiebei.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_shengdanlaoren.txt" />
    <None Include="Assets\GameRes\Config\tracks\xianshi_dangaoyu.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_mini_game1.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Add_Trail.shader" />
    <None Include="Assets\GameRes\Config\json\cj_fish_1639.json" />
    <None Include="Assets\GameRes\Config\path.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_crab_hammer_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_shuanglongzhengba_nuanchang.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_6\caishen_6.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_xiaoyaojianxian.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9204_4_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_daily_quest.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_promotion_rule.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\super_star_cunchu\spine\cqg_jinzhu.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\jintujifu\spine_chaika\chaika.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_ss_qinglong_new.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV2\CustomStandardV3.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_bignian_01_unfreeze.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_replay_grade.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jijia_daoshou.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jijia_yexiao.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_eyu2024.txt" />
    <None Include="Assets\GameRes\Shaders\CustomBloom.cginc" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\artifact\spine_jixianfeng\jijianfeng.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_mokun.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_DD_Additive.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\shoushi\bixin\bixin.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_achievement_key.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_monopoly.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_shengdanlaoren_new.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_meirenyu.txt" />
    <None Include="Assets\PlayMaker\Resources\PlayMakerAssemblies.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\login_cg\spine_loading3\sds_loging3.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_binglong_nuanchang.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_8\dijing_8.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\tide_yuqun02.txt" />
    <None Include="Assets\PlayMaker\Actions\MathExpression\Mathos\MathosLicense.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_leishenchui01.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_2023_christmas_pass.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_nangua.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetMaterial\Material Scanner_战宠.json" />
    <None Include="Assets\GameRes\Config\ct\ct_user_level.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_qingshi.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_sds_shuangzizuo.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_longzhou.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetFBX\FBX Scanner_BOSS.json" />
    <None Include="Assets\Fishes\Wings\PrefabArt\wings_3901\spine\wing_feather.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\season_equipment\spine_jijialongxi_2\jijialongxi_2.json" />
    <None Include="Assets\GameRes\Config\ct\ct_egg_ranking.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_crab_plate.txt" />
    <None Include="Assets\GameRes\Config\tracks\xianshi_ruyidai_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9201_3.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Artist_Effect\Effect_FullEffect.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_huolong_nuanchang.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetTexture\Texture Scanner_UI图集.json" />
    <None Include="Assets\GameRes\Config\tracks\string_9202_3_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_taikongmao.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_liandanlu.txt" />
    <None Include="Assets\GameRes\Config\tracks\summon_08_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_liandanlu_portrait.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetTexture\Texture Scanner_炮台.json" />
    <None Include="Assets\GameRes\Config\ct\ct_dictionary_hint_words_zhuoyuebaodian.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_juxiezuo.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\jianbaodashi\spine_ren_kanbanniang\ren_kanbanniang.json" />
    <None Include="Assets\GameRes\Config\tracks\group_405_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_mds_aiyagesi.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_dictionary_hint_words_jinjiebaodain.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\shoushi\hedaocai\hedaocai.json" />
    <None Include="Assets\GameRes\Config\tracks\string_9201_4_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV2\CustomGlobalIllumination.cginc" />
    <None Include="Assets\GameRes\Config\ct\ct_shop_hot_source.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_01_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_0104.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetFBX\FBX Scanner_战神.json" />
    <None Include="Assets\GameRes\Config\ct\ct_cardsys.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_dictionary_hint_words.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_leishenchui01_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\CustomShuipingzuo03.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_eyu01.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_17.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_cardsys_info.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_weapon_upgrade.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_zhubajie_nuanchang.txt" />
    <None Include="Assets\GameRes\Shaders\PBR\CustomStandardCoreForward.cginc" />
    <None Include="Assets\GameRes\Config\tracks\group_0103.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jinjiao.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_tslm_antianshi.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Surface.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_shengxiaolong_unfreeze.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_tieshangongzhu_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_likui_xiao.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28012_tangseng\room_28012.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_Dissolve_AlphaBlend.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_item_privilege.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_9\dijing_9.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_nangua_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_2023_sign_pass.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\spring_festival_2025\spine\jinglingtianshi\jinglingtianshi.json" />
    <None Include="Assets\GameRes\Config\tracks\lamp_20_portrait.txt" />
    <None Include="Assets\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\login_cg\spine_chukou\chukou.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9202_2_portrait.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_binglong.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_7\caishen_7.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28011_2\zhugeliang1.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_jinzhu.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\OutlineEfectOpaque.shader" />
    <None Include="Assets\GameRes\Config\tracks\special_zuantou.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jixiesha_chaoxiao.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_taiqiunvlang_xiao.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9203_1_portrait.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_cannon_speciality.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_06.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_xiaomonv_portrait.txt" />
    <None Include="Assets\GameSetting\ScanReport\Dependency\Dependency Effect Schema_Effect.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28011_2\zhugeliang1.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_0201.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_tusiji_inferior.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9202_3.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28011_1\zhugeliang.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV2\CustomStandardV2.shader" />
    <None Include="Assets\GameRes\Shaders\Effect\Artist_Effect\EyeballEffect.shader" />
    <None Include="Assets\GameRes\Config\tracks\group_0302_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_longgui_chaoxiao.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jinlong.txt" />
    <None Include="Assets\GameRes\Shaders\Effect_distortion_add.shader" />
    <None Include="Assets\GameRes\Shaders\OutlinePrePass.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_jiulianbaodeng_nuanchang.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_zhadan01_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_xuchu1.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9204_2.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lion\spine_jinchan\jinchan.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\marketplace_main\spine\taishanglaojun.json" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetBitmap\Bitmap Scanner_Font.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_moyuan.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_cannon_method.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_change_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_vip_item_bg.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_likui_taotie.txt" />
    <None Include="Assets\[废弃]\TA_Test\yucai\Shaders\FishCginc.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_xy_lijing.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetPrefab\Prefab Scanner_翅膀.json" />
    <None Include="Assets\[废弃]\TA_Test\yucai\Shaders\CustomGui_test.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_language_localize.txt" />
    <None Include="Assets\GameRes\Config\tracks\tide_yuqun01_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_call_saint_seiya.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\shayu\bqb_shayu.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_tusiji.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_07.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_ax_juxiezuo.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\artifact_coming\spine_shenqibaoku\artifact_coming_shenqibaoku.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_dragon_wish_big_icon.txt" />
    <None Include="Assets\Fishes\FishBoss\PrefabArt\1612\Spine\panlong.atlas.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMPro_Properties.cginc" />
    <None Include="Assets\GameRes\Config\ct\ct_small_game_center.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_likui_zhong.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_zhulong01.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28012_tangseng\room_28012.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_sds_tianxiezuo.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_2024_new_year_pass.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_shuangzizuo.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_1\caishen_1.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_endless_treasures_skin.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_star_checkin_days_30.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_15_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\Hair\CGIncludes\UnityAnisotropicCoreForwardSimple.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_taikongmao_portrait.txt" />
    <None Include="Assets\[废弃]\TA_Test\yuyang\Shader\Mobile-Diffuse.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_skin_cannon_sortie.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28008\room_28008.json" />
    <None Include="Assets\GameRes\Config\tracks\tide_yuqun04_portrait.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lion\spine_shizi\shizi.json" />
    <None Include="Assets\GameRes\Config\ct\ct_dailytask_pass.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_5\caishen_5.json" />
    <None Include="Assets\GameRes\Config\tracks\group_guide_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_0303.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_caishen.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Mobile-Particle-Add-Shine.shader" />
    <None Include="Assets\GameRes\Config\tracks\string_9203_4_portrait.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_5\joker_5.json" />
    <None Include="Assets\GameRes\Config\ct\ct_lottery_new.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_jinchan_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_egg_task.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetTexture\Texture Scanner_特效.json" />
    <None Include="Assets\GameRes\Config\tracks\zhadan_03.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_lunpanxie.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_gift_mall_tab_C.txt" />
    <None Include="Assets\GameRes\Config\tracks\shijian_moyuan_01.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\effectRain.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_endless_treasures_repair_item.txt" />
    <None Include="Assets\GameRes\Shaders\FurShader.shader" />
    <None Include="Assets\GameRes\Config\tracks\tide_yuqun06.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMPro_Surface.cginc" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_gift_free.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28005\room_28005.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_lucky_lot.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_series.txt" />
    <None Include="Assets\Fishes\Wings\PrefabArt\wings_3910\spine\wing_10.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\PBR\CustomStandardCoreForwardSimple.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_tianxiezuo.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_new_level.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jixiesha_nuanchang.txt" />
    <None Include="Assets\GameRes\Shaders\CustomBloom.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\spring_festival_2025\spine\zhitianshi\zhitianshi.json" />
    <None Include="Assets\GameRes\Config\atlas_ext.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_charge_ship.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_0105.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_paotai.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_shenwei_system_source.txt" />
    <None Include="Assets\GameRes\Shaders\Post Processing\Common\Shaders\ToneMappingShader.shader" />
    <None Include="Assets\Fishes\Wings\PrefabArt\wings_3909\spine\wing_9.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\LightingCustom.cginc" />
    <None Include="Assets\GameRes\Config\tracks\tide_yuqun01.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_0302_infinite_treasure.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_skin.txt" />
    <None Include="Assets\GameRes\Config\tracks\summon_05.txt" />
    <None Include="Assets\ThirdPart\Proxima\WebSocketSharp\README.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\anni4\spine\zhuque.json" />
    <None Include="Assets\Fishes\FishBoss\PrefabArt\1416\Spine\1416_longhunzhu_effect.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_aijiyanhou.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\artifact\spine_sunwukong\sunwukong.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_anheimolong.txt" />
    <None Include="Assets\GameRes\Shaders\SDS_Shader 2.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_longgui_nuanchang.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jijia_yanmo.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_dock_game_left_seq.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\G_dissolve_add2.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_xiaohuangya.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_crab_drill.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_lottery.txt" />
    <None Include="Assets\ThirdPart\Proxima\WebSocketSharp\LICENSE.txt" />
    <None Include="Assets\Effect\model\[新模型放在model2文件夹].txt" />
    <None Include="Assets\GameRes\Shaders\StylizedWater.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_8\caishen_8.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jianmo_inferior.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_Basic_Polar.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_sg_jiaxu.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Fresnel_Add.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_achievement_fish_new.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_item_box.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lion\spine_jinchan\jinchan.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_jixiesha.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28011_zhugeliang\28011.json" />
    <None Include="Assets\GameRes\Config\tracks\zhadan_04_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_15.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_update_gift_client.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_xy_laojun2024.txt" />
    <None Include="Assets\GameRes\Config\tracks\summon_02_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\tide_hama01_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_alading.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_competition.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_kaimendaji.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_crab_plate_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_change.txt" />
    <None Include="Assets\MiniGamesRes\common\PrefabArt\mini_common\spine\login\shark.json" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_gift_free_old.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_personal_info.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28013_tianshi\tianshi.json" />
    <None Include="Assets\GameRes\Config\tracks\string_9201_2_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_zsgift_one_versioninfo.txt" />
    <None Include="Assets\GameRes\Config\json\cj_boss_idmap.json" />
    <None Include="Assets\GameRes\Shaders\PBR\CustomStandardCore.cginc" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_4\caishen_4.json" />
    <None Include="Assets\GameRes\Shaders\PBR\CustomImageBasedLighting.cginc" />
    <None Include="Assets\GameSetting\ScanReport\AssetMaterial\Material Scanner_翅膀.json" />
    <None Include="Assets\GameRes\Shaders\PBRV3\Hair_Transparent.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_shenwei_system_skill.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_1\caishen_1.json" />
    <None Include="Assets\GameRes\Config\json\cj_festa_9th_movie.json" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_LightFlow_ColorAdd.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_2\joker_2.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_pocket_shop.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_privilege_old.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9201_4.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_18.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_meirenyu_nuanchang.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV2\CustomStandardBRDF.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_tslm_meimo.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_2\caishen_2.json" />
    <None Include="Assets\HybridCLRGenerate\link.xml" />
    <None Include="Assets\GameRes\Config\tracks\group_403.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jianmo.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_1\joker_1.json" />
    <None Include="Assets\GameRes\Shaders\NPR\ShaderLibrary\CartoonInvLerpRemap.cginc" />
    <None Include="Assets\TextMesh Pro\Preset\FontSource\fallback.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_boss_gift_bossinfo.txt" />
    <None Include="Assets\GameRes\Config\tracks\tide_yuqun02_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_3zhounian.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_eyu.txt" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_daishu.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_8\caishen_8.json" />
    <None Include="Assets\GameRes\Config\tracks\lamp_11.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\forge\spine\xuchu.json" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_HeatDistortion_Simple.shader" />
    <None Include="Assets\GameRes\Config\tracks\string_9204_3_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_xiaomonv_xiao_portrait.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\artifact_coming\spine_houzi\houzi.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\UI\UI-ColorMask.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_team_buying_gift.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_team_buying_title.txt" />
    <None Include="Assets\[废弃]\TA_Test\yuyang\Shader\Mobile-VertexLit.shader" />
    <None Include="Assets\GameRes\Shaders\Diffuse.shader" />
    <None Include="Assets\GameRes\Shaders\Effect\Artist_Effect\Depth Mask.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_egg_tips.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\shoushi\touxiang\touxiang.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_1\dijing_1.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_gift_mall_gift_B.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_tlmtquest.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_star_checkin_days_31.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9203_2_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_19.txt" />
    <None Include="Assets\Fishes\Wings\PrefabArt\wings_3902\spine\wing_technology.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Sanguo_fresnel.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_xinshoubaodian.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_moyuan1540.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28001_meirenyu\room_28001.json" />
    <None Include="Assets\GameRes\Config\tracks\special_lunpanxie_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_language_key_dw.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_sds_shuipingzuo.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_xy_yangjian.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_glory_cultivation.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_boss_gift_versioninfo.txt" />
    <None Include="Assets\GameRes\Config\tracks\summon_08.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_shenwei_cannon.txt" />
    <None Include="Assets\GameRes\Config\json\cj_season_equipment_r3_v6.json" />
    <None Include="Assets\GameRes\Config\ct\ct_growth_help_new.txt" />
    <None Include="Assets\GameRes\Config\tracks\zhadan_04.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_2\dijing_2.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_sds_mojiezuo.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_longgui_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\tide_yuqun06_portrait.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\login_cg\spine_loading3\sds_loging3.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\growth_help_new\spine\xiaochou.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_endless_treasures_item.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jixiesha01.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_boss_broadcast_config.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jinzhu_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_skin_cannon.txt" />
    <None Include="Assets\GameRes\Shaders\Post Processing\Common\Shaders\BloomCombineShader.shader" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_Basic_AlphaBlend.shader" />
    <None Include="Assets\GameRes\Config\tracks\group_0301.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_achievement_career.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_fish_frame.txt" />
    <None Include="Assets\GameRes\Config\path_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9201_3_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\tide_hama01.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\FishCginc.cginc" />
    <None Include="Assets\GameRes\Shaders\PBRV2\CustomStandardCoreForwardSimple.cginc" />
    <None Include="Assets\GameRes\Config\tracks\summon_07_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_caidan2024.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_festa_festival_2024_02.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\DisappareParticleAlphaBlend.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\artifact\spine_liumangxing\liumangxing.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\PBR\CustomStandard.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_denglongyu01.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\shoushi\hedaocai\hedaocai.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_achievement_activity.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jijia_shayu.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_ss_qinglong_sheng.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\spring_festival_2025\spine\shengtianshi\shengtianshi.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_call_saint_seiya_reward.txt" />
    <None Include="Assets\GameRes\Config\tracks\zhadan_02.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28012\room_28012.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\TechY\BG_Caustics_BossMask.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_9\dijing_9.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28002_eryv\room_28002.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_jianmo_nuanchang.txt" />
    <None Include="Assets\GameRes\Shaders\CustomBloom1.cginc" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28011_zhugeliang\28011.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Effect_lc_baozha.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_sunwukong_nuanchang.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_zhanshen_gift2.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_shuanglongzhengba_inferior.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetTexture\Texture Scanner_BOSS.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28007\room_28007.atlas.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetPrefab\Prefab Scanner_炮台.json" />
    <None Include="Assets\GameRes\Config\tracks\xianshi_xiaochou_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_dailyquest_lucky.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_personal_dec.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_05.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_06_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_dailytask_weekly.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_shizizuo.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\particle_distort_add.shader" />
    <None Include="Assets\GameSetting\ScanReport\AssetMaterial\Material Scanner_UI面板.json" />
    <None Include="Assets\GameRes\Config\ct\ct_boss_drop.txt" />
    <None Include="Assets\GameRes\Shaders\PBR_Character.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_6\dijing_6.atlas.txt" />
    <None Include="Assets\Fishes\Wings\PrefabArt\wings_3903\spine\wing_butterfly.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28001\room_28001.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\spring_festival_2025\spine\jinglingtianshi\jinglingtianshi.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\artifact\spine_sunwukong\sunwukong.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_star_checkin_icon.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_nezha_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\bignian_01.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_module_unlock.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\LightingCustomBase.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_dishu.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_cannon_bullet.txt" />
    <None Include="Assets\GameRes\Shaders\PBR\CustomStandardMeta.cginc" />
    <None Include="Assets\GameRes\Config\ct\ct_achievement.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_fenghuang.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_shuanglongzhengba.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\UnityCG.cginc" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_star_checkin_vip.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_crab_hammer.txt" />
    <None Include="Assets\Fishes\Wings\PrefabArt\wings_3910\spine\wing_10.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\login_cg\spine_chukou\chukou.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28001\room_28001.atlas.txt" />
    <None Include="Assets\ThirdPart\Proxima\link.xml" />
    <None Include="Assets\GameRes\Config\json\cj_season_equipment_r2_v3.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_tslm_duotianshi.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_artifact_coming_ui.txt" />
    <None Include="Assets\GameRes\Shaders\Hair\CGIncludes\UnityAnisotropicLighting.cginc" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28008\room_28008.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_tslm_jingling.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9201_1_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_call_saint_seiya_choice.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\marketplace_main\spine\taishanglaojun.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_4\joker_4.atlas.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\FishStandard_Base.shader" />
    <None Include="Assets\GameRes\Shaders\TechY\MatcapTrans.shader" />
    <None Include="Assets\GameRes\Config\tracks\summon_05_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\3DShadow.shader" />
    <None Include="Assets\GameRes\Config\tracks\zhadan_01_portrait.txt" />
    <None Include="Assets\GameRes\Config\json\cj_vip_old.json" />
    <None Include="Assets\GameRes\Config\ct\ct_festa_9th_movie.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_0102.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_01.txt" />
    <None Include="Assets\GameRes\Config\json\cj_hd.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\zhanshenxunmeng\spine_lijing\lijing.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_prod_list.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_zhubajie.txt" />
    <None Include="Assets\GameRes\Shaders\NPR\FishCustomCartoon.shader" />
    <None Include="Assets\GameRes\Shaders\PBRV3\Skin_FastSSS.shader" />
    <None Include="Assets\GameRes\Shaders\SDS_Hair.shader" />
    <None Include="Assets\GameRes\Config\tracks\boss_meirenyu01.txt" />
    <None Include="Assets\GameRes\Config\tracks\xiongmao_01_portrait.txt" />
    <None Include="Assets\[废弃]\TA_Test\yucai\Shaders\UnityCG.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_denglongyu_nuanchang.txt" />
    <None Include="Assets\GameRes\Config\tracks\zhadan_02_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\zhadan_03_portrait.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\zhanshenxunmeng\spine_taishanglaojun\taishanglaojun.json" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_privilege.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetTexture\Texture Scanner_UI图片.json" />
    <None Include="Assets\GameRes\Config\ct\ct_vip_lv_grade_tag.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV2\CustomLighting.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_mds_ladamandisi.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\shayu\bqb_shayu.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_shuangyuzuo.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_newshare.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28007\room_28007.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_1\dijing_1.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\anni4\spine\zhuque.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\adventure_box\spine\yaoshi.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_yinjiao_inferior.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_gift_paid_old.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_mokun_nuanchang.txt" />
    <None Include="Assets\[废弃]\TA_Test\yucai\Shaders\FishStandard_test.shader" />
    <None Include="Assets\Fishes\CannonSkin\PrefabVip\VIP战神皮肤.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\jintujifu\spine_chaika\chaika.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28013\laohu_effect.json" />
    <None Include="Assets\GameRes\Shaders\CustomBloom1.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\dw\spine_duanwei_qizi\duanwei_qizi.atlas.txt" />
    <None Include="Assets\Fishes\Wings\PrefabArt\wings_3909\spine\wing_9.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_daishu_portrait.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28013_1\tianshi_bg.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_item_new.txt" />
    <None Include="Assets\Effect\texture\fx_tex_gp1_xxx.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28012\room_28012.json" />
    <None Include="Assets\GameRes\Shaders\Post Processing\Common\Shaders\ChromaticAbberationShader.shader" />
    <None Include="Assets\GameRes\Config\json\cj_vip_new.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\zhanshenxunmeng\spine_lijing\lijing.json" />
    <None Include="Assets\[废弃]\juxie_dlw\spine\juxie_dlw.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_xy_laojun.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_17_portrait.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\28002_eryv\room_28002.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_anheimolong_unfreeze.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV3\LightingCustomBase_Test.cginc" />
    <None Include="Assets\GameRes\Config\ct\ct_zhebaoge_item_mars_adapter.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_0205.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_tslm_emo.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_xiaochou.txt" />
    <None Include="Assets\[废弃]\TA_Test\yucai\Shaders\LightingCustom.cginc" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF Overlay.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_7\joker_7.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_experssion.txt" />
    <None Include="Assets\GameRes\Config\json\cj_num_color_code_map.json" />
    <None Include="Assets\GameRes\Config\ct\ct_shop_hot.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9203_4.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\shoushi\dianzan\dianzan.atlas.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_asset_update_tag.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28013_1\tianshi_bg.json" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_material.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_guide.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\artifact_coming\spine_shenqibaoku\artifact_coming_shenqibaoku.json" />
    <None Include="Assets\GameRes\Shaders\UI\MaskDefault.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_match_task.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_Disturbance_AlphaBlend.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_angel_skill.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_xibuniuzai.txt" />
    <None Include="Assets\GameRes\Shaders\PBRV2\CustomImageBasedLighting.cginc" />
    <None Include="Assets\GameRes\Config\tracks\shijian_moyuan_01_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\Post Processing\Common\Shaders\VignetteShader.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_3\caishen_3.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_16_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_tslm_shengtianshi.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\login_cg\spine_login_shenshou\shenshou_login.json" />
    <None Include="Assets\GameRes\Config\ct\ct_egg.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_cannon.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_longgui01.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetTexture\Texture Scanner_翅膀.json" />
    <None Include="Assets\GameRes\Shaders\Unlit-AlphaTest.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_material_old.txt" />
    <None Include="Assets\GameRes\Config\tracks\summon_07.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Bitmap-Mobile.shader" />
    <None Include="Assets\GameRes\Config\tracks\summon_01.txt" />
    <None Include="Assets\GameRes\Shaders\Hair\CGIncludes\UnityAnisotropicCore.cginc" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\growth_help_new\spine\xiaochou.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_ss_qilin.txt" />
    <None Include="Assets\GameRes\Shaders\CustomSpritesCore.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_xiankun_inferior.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\ice_fishing_game\spine_yugan\yugan.atlas.txt" />
    <None Include="Assets\[废弃]\TA_Test\yucai\Shaders\guiTest.shader" />
    <None Include="Assets\GameRes\Shaders\PBRV3\FurTransparent.shader" />
    <None Include="Assets\PlayMaker\Actions\Tween\EasingFunctionLicense.txt" />
    <None Include="Assets\GameRes\Config\json\cj_season_equipment_r1_v2.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\artifact\spine_pilihuo\pilihuochaifen_spine.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_6\dijing_6.json" />
    <None Include="Assets\GameRes\Config\tracks\special_leishenchui.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_cannon_enter_effect.txt" />
    <None Include="Assets\GameRes\Shaders\NPR\CustomCartoon.shader" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_VertWave_Additive.shader" />
    <None Include="Assets\GameRes\Shaders\Effect\Artist_Effect\Effect_ChannelOffset.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_dark_star_boss.txt" />
    <None Include="Assets\GameRes\Shaders\Hair\CGIncludes\UnityAnisotropicBRDF.cginc" />
    <None Include="Assets\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
    <None Include="Assets\GameSetting\AssetBundleCollectorConfigHW.xml" />
    <None Include="Assets\GameRes\Config\tracks\boss_chunvzuo.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_shenniu.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\caishen\caishen_7\caishen_7.json" />
    <None Include="Assets\GameRes\Config\tracks\group_0305.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_ss_xuanwu_xiao.txt" />
    <None Include="Assets\Fishes\Wings\PrefabArt\wings_3999\spine\wing_11.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\summon_04.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\spring_festival_2025\spine\zhitianshi\zhitianshi.atlas.txt" />
    <None Include="Packages\com.code-philosophy.hybridclr\package.json" />
    <None Include="Assets\GameSetting\AssetBundleCollectorConfig.xml" />
    <None Include="Assets\GameRes\Config\tracks\string_9202_1.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetMaterial\Material Scanner_战神.json" />
    <None Include="Assets\GameRes\Config\ct\ct_duanwu_task.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_nezha.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Artist_Effect\CutInMask.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_dark_star_reward.txt" />
    <None Include="Assets\GameSetting\ScanReport\BasePackage\Base Package Scanner_首包贴图.json" />
    <None Include="Assets\GameSetting\ScanReport\AssetTexture\Texture Scanner_战神.json" />
    <None Include="Assets\GameRes\Config\ct\ct_gift_mall_shop.txt" />
    <None Include="Assets\GameRes\Config\json\cj_ice_fishing.json" />
    <None Include="Assets\GameRes\Config\tracks\boss_caidan.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_big_monster_ranking.txt" />
    <None Include="Assets\GameRes\Shaders\FurHelper.cginc" />
    <None Include="Assets\GameRes\Config\ct\ct_cannon_gift_version_config.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_shengdanlaoren_2024.txt" />
    <None Include="Assets\GameRes\Config\json\cj_season_equipment_r3_v5.json" />
    <None Include="Assets\GameRes\Config\ct\ct_timematch.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jiuweihu.txt" />
    <None Include="Assets\GameRes\Shaders\UIGray.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\zhanshen_lottery\spine\jiaxu.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\adventure_box\spine\yaoshi.json" />
    <None Include="Assets\GameRes\Shaders\Hair\CGIncludes\UnityAnisotropicCommon.cginc" />
    <None Include="Assets\GameRes\Config\ct\ct_user_name.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_artifact_coming_access.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_sds_baiyangzuo.txt" />
    <None Include="Assets\Fishes\FishBoss\PrefabArt\1416\Spine\1416_longhunzhu_effect.json" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\zhanshenxunmeng\spine_yangjian\yangjian.atlas.txt" />
    <None Include="Assets\GameRes\Config\fishes.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile.shader" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\dijing\dijing_2\dijing_2.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\emoji\joker\joker_8\joker_8.atlas.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_405_infinite_treasure.txt" />
    <None Include="Assets\GameRes\Config\tracks\group_0201_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_longgui.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_item_fragment.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_caidan_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_daoyaotu.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_jiuweihu_nuanchang.txt" />
    <None Include="Assets\GameRes\Config\tracks\summon_06.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_moyuan1540_nuanchang.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_shouxing.txt" />
    <None Include="Assets\GameRes\Config\tracks\0.txt" />
    <None Include="Assets\GameRes\Config\tracks\boss_meirenyu_chaoxiao.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\login_cg\spine_login_shenshou\shenshou_login2.atlas.txt" />
    <None Include="Assets\GameRes\UIPanel\PrefabArt\lobby\spine\room_28003\room_28003.json" />
    <None Include="Assets\GameRes\Config\ct\ct_shenwei_system_privilege.txt" />
    <None Include="Assets\GameRes\Config\tracks\lamp_09.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_leishenchui_portrait.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetMaterial\Material Scanner_场景.json" />
    <None Include="Assets\Fishes\Cannon\PrefabArt3D\战神炮台的美术资源.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\SM_TransparentRim.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_gift_mall_hot.txt" />
    <None Include="Assets\GameRes\Config\tracks\shijian_01.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetMaterial\Material Scanner_展示.json" />
    <None Include="Assets\GameRes\Shaders\UIAdditive.shader" />
    <None Include="Assets\TextMesh Pro\Sprites\EmojiOne.json" />
    <None Include="Assets\PlayMaker\Actions\Tween\Readme.txt" />
    <None Include="Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\LICENSE.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_zsgift_one_bossinfo.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetMaterial\Material Scanner_炮台.json" />
    <None Include="Assets\GameRes\Config\tracks\summon_02.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_achievement_stage_reward.txt" />
    <None Include="Assets\GameRes\Config\tracks\string_9203_2.txt" />
    <None Include="Assets\GameRes\Config\json\cj_vip_privilege_expansion.json" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_LightFlow_AlphaBlend.shader" />
    <None Include="Assets\GameRes\Config\tracks\tide_yuqun05_portrait.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_shenwei_system.txt" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_material_info_old.txt" />
    <None Include="Assets\GameSetting\ScanReport\AssetPrefab\Prefab Scanner_BOSS.json" />
    <None Include="Assets\GameRes\Config\tracks\string_9204_2_portrait.txt" />
    <None Include="Assets\GameRes\Shaders\PBR\CustomLighting.cginc" />
    <None Include="Assets\GameRes\Config\json\cj_theme_ftb.json" />
    <None Include="Assets\GameRes\Shaders\Effect\T-Shaders\TY_Effect_Mask_Additive.shader" />
    <None Include="Assets\GameRes\Shaders\PBRV2\CustomStandardCoreForward.cginc" />
    <None Include="Assets\UWA\UWA_GOT\package.json" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Sprite.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_team_buying_status.txt" />
    <None Include="Assets\GameRes\Shaders\Effect\Effect_refringence.shader" />
    <None Include="Assets\GameRes\Shaders\Effect\particle_distort_alphaBlend.shader" />
    <None Include="Assets\GameRes\Config\tracks\group_0304.txt" />
    <None Include="Assets\GameRes\Shaders\Hair\HairShader.shader" />
    <None Include="Assets\GameRes\Shaders\NPR\CustomCartoon_Decal.shader" />
    <None Include="Assets\GameRes\Config\ct\ct_forge_level.txt" />
    <None Include="Assets\GameRes\Config\json\cj_music_festival_2023.json" />
    <None Include="Assets\GameRes\Config\tracks\string_9204_1_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\bignian_01_unfreeze_portrait.txt" />
    <None Include="Assets\Fishes\Wings\PrefabArt\wings_3902\spine\wing_technology.json" />
    <None Include="Assets\GameRes\Shaders\NPR\ShaderLibrary\AvatarGenshinOutlinePass.cginc" />
    <None Include="Assets\GameRes\Config\tracks\boss_denglongyu02.txt" />
    <None Include="Assets\GameRes\Shaders\NPR\ShaderLibrary\AvatarGenshinPass.cginc" />
    <None Include="Assets\GameRes\Config\tracks\xiongmao_01.txt" />
    <None Include="Assets\GameRes\Config\tracks\special_longhunzhu_portrait.txt" />
    <None Include="Assets\GameRes\Config\tracks\zhadan_01.txt" />
    <None Include="Assets\GameRes\Shaders\PBR\CustomStandardShadow.cginc" />
    <None Include="Assets\GameRes\Config\tracks\group_guide_goldfish.txt" />
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsNativeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsNativeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UNETModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PackageManagerUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.PackageManagerUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIServiceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIServiceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="ILRuntime.Mono.Cecil.Mdb">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\ILRuntime\Plugins\ILRuntime.Mono.Cecil.Mdb.dll</HintPath>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\DOTween\DOTween.dll</HintPath>
    </Reference>
    <Reference Include="LZ4">
      <HintPath>C:\Users\<USER>\Documents\game\client\Packages\com.code-philosophy.hybridclr\Plugins\LZ4.dll</HintPath>
    </Reference>
    <Reference Include="UWAShared">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\UWA\UWA_SDK\Runtime\ManagedLibs\UWAShared.dll</HintPath>
    </Reference>
    <Reference Include="proxima-websocket-sharp">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\ThirdPart\Proxima\WebSocketSharp\proxima-websocket-sharp.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\ICSharpCode.SharpZipLib\netstandard2.0\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="PlayMaker">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\PlayMaker\PlayMaker.dll</HintPath>
    </Reference>
    <Reference Include="Ionic.Zlib">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\Ionic.Zlib.dll</HintPath>
    </Reference>
    <Reference Include="dnlib">
      <HintPath>C:\Users\<USER>\Documents\game\client\Packages\com.code-philosophy.hybridclr\Plugins\dnlib.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\JsonDotNet\Assemblies\Standalone\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="ILRuntime.Mono.Cecil.Pdb">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\ILRuntime\Plugins\ILRuntime.Mono.Cecil.Pdb.dll</HintPath>
    </Reference>
    <Reference Include="zxing.unity">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\zxing.unity.dll</HintPath>
    </Reference>
    <Reference Include="ConditionalExpression">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\PlayMaker\ConditionalExpression.dll</HintPath>
    </Reference>
    <Reference Include="ILRuntime.Mono.Cecil">
      <HintPath>C:\Users\<USER>\Documents\game\client\Assets\Plugins\ILRuntime\Plugins\ILRuntime.Mono.Cecil.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\ref\2.0.0\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Net.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VSCode.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.VSCode.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CacheServer">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\UnityEditor.CacheServer.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Cursor.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.Cursor.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.Sprite.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.2D.Sprite.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.DeviceSimulator.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.DeviceSimulator.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.dll</HintPath>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline.Editor">
      <HintPath>C:\Users\<USER>\Documents\game\client\Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.Editor.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Assembly-CSharp-firstpass.csproj">
      <Project>{a0431b3a-6e27-fb34-197e-1acde9001a0f}</Project>
      <Name>Assembly-CSharp-firstpass</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Machine.csproj">
      <Project>{c46542a4-cad3-0a16-0742-cec97b7020de}</Project>
      <Name>UniFramework.Machine</Name>
    </ProjectReference>
    <ProjectReference Include="HybridCLR.Runtime.csproj">
      <Project>{7dacd347-4b99-43ff-7bd2-399821e768b0}</Project>
      <Name>HybridCLR.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Network.csproj">
      <Project>{32f18cff-ad0a-fac8-883d-ac8796c2bedc}</Project>
      <Name>UniFramework.Network</Name>
    </ProjectReference>
    <ProjectReference Include="YooAsset.Editor.csproj">
      <Project>{8d9f312e-1d9e-9a0a-b421-889026abeeca}</Project>
      <Name>YooAsset.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Singleton.csproj">
      <Project>{87944ebe-9e82-036b-18cb-a9f80c3fa62a}</Project>
      <Name>UniFramework.Singleton</Name>
    </ProjectReference>
    <ProjectReference Include="Proxima.Editor.csproj">
      <Project>{8ea8bc99-78ec-b440-8f0a-591fd1f43326}</Project>
      <Name>Proxima.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Event.csproj">
      <Project>{ef7c93d4-6571-47fa-6170-a3558a123916}</Project>
      <Name>UniFramework.Event</Name>
    </ProjectReference>
    <ProjectReference Include="spine-unity.csproj">
      <Project>{ce5d6b30-a2dd-b9db-65ae-bf6bf68ceff5}</Project>
      <Name>spine-unity</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Tween.csproj">
      <Project>{79a1243a-51ed-79d2-d0c6-d9fe5c9f779d}</Project>
      <Name>UniFramework.Tween</Name>
    </ProjectReference>
    <ProjectReference Include="GameRuntime.csproj">
      <Project>{384add0f-ab48-6bb7-0d2f-8a5f173dc79c}</Project>
      <Name>GameRuntime</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Animation.csproj">
      <Project>{108da027-93aa-7cb7-ac76-cecce677c424}</Project>
      <Name>UniFramework.Animation</Name>
    </ProjectReference>
    <ProjectReference Include="Proxima.csproj">
      <Project>{c0720ac4-aec5-5da5-a31e-a887f067c27e}</Project>
      <Name>Proxima</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnityResonanceEditor.csproj">
      <Project>{4d2d783a-89e5-af18-93e4-1cd1706fd8dd}</Project>
      <Name>FMODUnityResonanceEditor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Postprocessing.Editor.csproj">
      <Project>{258d3d40-386b-3526-f193-f03d28a8b899}</Project>
      <Name>Unity.Postprocessing.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Pooling.csproj">
      <Project>{fefe0f0d-5327-4908-feb7-148a7048665c}</Project>
      <Name>UniFramework.Pooling</Name>
    </ProjectReference>
    <ProjectReference Include="HybridCLR.Editor.csproj">
      <Project>{abcdaff7-8ceb-ea50-aeb8-4f5402d7e818}</Project>
      <Name>HybridCLR.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="youhu.unity_uwa_sdk.Editor.csproj">
      <Project>{1829fc53-2793-8581-c2b2-ed4bcc7f8bf6}</Project>
      <Name>youhu.unity_uwa_sdk.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="RBG.Mulligan.csproj">
      <Project>{cbfc065b-8b45-689e-91b4-64cd9742c8b4}</Project>
      <Name>RBG.Mulligan</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Window.csproj">
      <Project>{f23b84e2-3bd6-49ef-de9b-ccea0def86fb}</Project>
      <Name>UniFramework.Window</Name>
    </ProjectReference>
    <ProjectReference Include="youhu.unity_uwa_sdk.csproj">
      <Project>{b53f86b4-8f62-c0d4-94f8-4b9e284011ad}</Project>
      <Name>youhu.unity_uwa_sdk</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Postprocessing.Runtime.csproj">
      <Project>{be55b292-cc44-17c8-5069-aa98556bdc44}</Project>
      <Name>Unity.Postprocessing.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="MeshEditor.Effects.RunTime.csproj">
      <Project>{a1e39cda-e28c-97a5-aff4-2a916ec95569}</Project>
      <Name>MeshEditor.Effects.RunTime</Name>
    </ProjectReference>
    <ProjectReference Include="NativeGallery.Editor.csproj">
      <Project>{58a638ce-8fe9-ad75-5f36-6492c5455e30}</Project>
      <Name>NativeGallery.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniFramework.Utility.csproj">
      <Project>{7c28f787-7f16-1728-6d1f-db87d0ed1482}</Project>
      <Name>UniFramework.Utility</Name>
    </ProjectReference>
    <ProjectReference Include="Coffee.CFX_Demo_With_UIParticle.csproj">
      <Project>{a8b0ee9f-f9e9-f15d-63a5-a175e53896d7}</Project>
      <Name>Coffee.CFX_Demo_With_UIParticle</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnity.csproj">
      <Project>{b2a04fda-593f-2822-75b6-7a0ce85de5c8}</Project>
      <Name>FMODUnity</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnityEditor.csproj">
      <Project>{ac2f2cea-5f4d-2521-9100-a99822067e71}</Project>
      <Name>FMODUnityEditor</Name>
    </ProjectReference>
    <ProjectReference Include="NativeGallery.Runtime.csproj">
      <Project>{b25065d1-1177-4f4e-abc4-806c113ca925}</Project>
      <Name>NativeGallery.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnityResonance.csproj">
      <Project>{710aab89-2b5d-111c-d2b6-4b78886180d2}</Project>
      <Name>FMODUnityResonance</Name>
    </ProjectReference>
    <ProjectReference Include="spine-unity-editor.csproj">
      <Project>{c16d3fc2-d438-a49f-0d35-4d45f5039eb5}</Project>
      <Name>spine-unity-editor</Name>
    </ProjectReference>
    <ProjectReference Include="YooAsset.csproj">
      <Project>{8adfa466-57e6-7a4c-bea4-51b7aecf0597}</Project>
      <Name>YooAsset</Name>
    </ProjectReference>
    <ProjectReference Include="Coffee.UIParticle.csproj">
      <Project>{04540531-31b5-ecaf-3050-921f628688e1}</Project>
      <Name>Coffee.UIParticle</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
